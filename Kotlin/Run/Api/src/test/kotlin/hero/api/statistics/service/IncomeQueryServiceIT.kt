package hero.api.statistics.service

import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class IncomeQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetExpectedIncome {
        @ParameterizedTest
        @CsvSource(
            value = [
                "US,1170,10.0",
                "CZ,1142,12.1",
            ],
        )
        fun `should get income only from active subs that are not cancelled`(
            companyCountry: String,
            expectedNetIncomeCents: Int,
            expectedFeePercent: Double,
        ) {
            val underTest = IncomeQueryService(TestCollections.usersCollection, lazyTestContext)
            testHelper.createUser("cestmirstrakatyheroherorpkjaolu", companyCountry = companyCountry)
            testContext.execute(
                """
insert into public.subscription (stripe_id, customer_id, creator_id, started_at, ends_at, status, currency, user_id, tier_id, price_cents, created_at, updated_at, cancelled_at, ended_at, cancellation_reason, coupon_percent_off, coupon_expires_at)
        -- active but cancelled
values  ('sub_1MmeRmB6ZCHekl2RyKbVBado', 'cus_NXjurDXjGBJjCd', 'cestmirstrakatyheroherorpkjaolu', now() , now() + '1 day'::interval, 'active', 'EUR', 'cestmirstrakatyheroherorpkjaolu', 'EUR05', 500, '2024-01-17 14:40:10.670571', '2024-03-08 15:09:00.577723', now(), null, 'cancellation_requested', null, null),
        -- not active
        ('sub_1MClhxB6ZCHekl2RQh5hQXDS', 'cus_J0gOkJpN1jWOD0', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'incomplete', 'EUR', 'cestmirstrakatyheroherorpkjaolu', 'EUR05', 500, '2024-01-08 15:09:09.026134', '2024-03-08 15:08:42.530235', null, null, null, null, null),
        -- active
        ('sub_1Os50iB6ZCHekl2RBtkcLuPD', 'cus_PhTyOxFkf4lZOo', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'active', 'EUR', 'cestmirstrakatyheroherorpkjaolu', 'EUR03', 300, '2024-03-08 15:07:05.488011', '2024-03-08 15:07:05.488011', null, null, null, null, null),
        -- active
        ('sub_1Os51rB6ZCHekl2RkOZdyUQO', 'cus_PhTzE3MsnCxJ56', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'past_due', 'EUR', 'cestmirstrakatyheroherorpkjaolu', 'EUR04', 400, '2024-03-08 15:08:16.306886', '2024-03-08 15:08:16.306886', null, null, null, null, null),
        -- active with coupon
        ('sub_1NbgcwB6ZCHekl2RbzDIKtZ9', 'cus_OER64DPfT2g21P', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'active', 'EUR', 'cestmirstrakatyheroherorpkjaolu', 'EUR04', 400, '2024-03-08 15:08:16.306886', '2024-03-08 15:08:16.306886', null, null, null, 50, now() + '1 day'::interval),
        -- active with expired coupon
        ('sub_1NbgcNB6ZCHekl2RcYDp8Vbd', 'cus_KUHMd0dyasy40l', 'cestmirstrakatyheroherorpkjaolu', now(),  now() + '1 day'::interval, 'active', 'EUR', 'cestmirstrakatyheroherorpkjaolu', 'EUR04', 400, '2024-03-08 15:08:16.306886', '2024-03-08 15:08:16.306886', null, null, null, 50, now() - '1 day'::interval)
                """,
            )

            val result = underTest.execute(GetExpectedIncome("cestmirstrakatyheroherorpkjaolu"))

            assertThat(result.grossIncomeCents).isEqualTo(1300)
            assertThat(result.netIncomeCents).isEqualTo(expectedNetIncomeCents)
            assertThat(result.feePercents).isEqualTo(expectedFeePercent)
        }

        @Test
        fun `creator with no subscriptions should not throw and should return 0`() {
            val underTest = IncomeQueryService(TestCollections.usersCollection, lazyTestContext)
            testHelper.createUser("wveygkstvcjjc")

            val result = underTest.execute(GetExpectedIncome("wveygkstvcjjc"))
            assertThat(result.grossIncomeCents).isEqualTo(0)
            assertThat(result.netIncomeCents).isEqualTo(0)
        }
    }
}
