package hero.api.post.service

import hero.api.post.service.dto.PostInput
import hero.baseutils.minus
import hero.baseutils.mockNow
import hero.baseutils.plus
import hero.baseutils.truncated
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaUploadsService
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Clock
import java.time.Instant
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

class CreatorPostCommandServiceIT : IntegrationTest(mockInstantNow = true) {
    @Nested
    inner class CreateCreatorPost {
        @Test
        fun `should create creator post`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf(), "delta")
            val result = underTest.execute(
                CreateCreatorPost(
                    "cestmir",
                    attributes,
                    setOf("sports"),
                    isSponsored = true,
                    isAgeRestricted = true,
                    communityId = null,
                ),
            )

            assertThat(result).isEqualTo(TestCollections.postsCollection[result.id].get())
            assertThat(result.text).isEqualTo("Textik")
            assertThat(result.textHtml).isEqualTo("<p>Textik</p>")
            assertThat(result.textDelta).isEqualTo("delta")
            assertThat(result.categories).isEqualTo(listOf("sports"))
            assertThat(result.userId).isEqualTo("cestmir")
            assertThat(result.parentUserId).isEqualTo("cestmir")
            assertThat(result.isSponsored).isTrue()
            assertThat(result.isAgeRestricted).isTrue()
        }

        @Test
        fun `only community members can create threads in community`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf(), "delta")

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        CreateCreatorPost(
                            "pepa",
                            attributes,
                            setOf(),
                            isSponsored = true,
                            isAgeRestricted = true,
                            communityId = community.id,
                        ),
                    )
                }
                .withMessage("User pepa cannot post in community ${community.id}")
        }

        @Test
        fun `community owner can always create thread even if he is not member`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf(), "delta")

            assertThatNoException().isThrownBy {
                underTest.execute(
                    CreateCreatorPost(
                        "cestmir",
                        attributes,
                        setOf(),
                        isSponsored = true,
                        isAgeRestricted = true,
                        communityId = community.id,
                    ),
                )
            }
        }

        @Test
        fun `should create community thread`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            testHelper.createCommunityMember(community.id, "cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf(), "delta")
            val result = underTest.execute(
                CreateCreatorPost(
                    "cestmir",
                    attributes,
                    setOf("sports"),
                    isSponsored = true,
                    isAgeRestricted = true,
                    communityId = community.id,
                ),
            )

            assertThat(result).isEqualTo(TestCollections.postsCollection[result.id].get())
            assertThat(result.text).isEqualTo("Textik")
            assertThat(result.textHtml).isEqualTo("<p>Textik</p>")
            assertThat(result.textDelta).isEqualTo("delta")
            assertThat(result.categories).isEqualTo(listOf("sports"))
            assertThat(result.userId).isEqualTo("cestmir")
            assertThat(result.parentUserId).isEqualTo("cestmir")
            assertThat(result.isSponsored).isTrue()
            assertThat(result.isAgeRestricted).isTrue()
            assertThat(result.communityId).isEqualTo(community.id.toString())
        }

        @Test
        fun `publishedAt cannot be more than 5 minutes in the past`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(
                    CreateCreatorPost(
                        "cestmir",
                        attributes,
                        setOf(),
                        publishedAt = Instant.now() - 6.minutes,
                        isSponsored = true,
                        isAgeRestricted = true,
                        communityId = null,
                    ),
                )
            }
        }

        @Test
        fun `categories that are not defined by the creator should not be allowed`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")
            testHelper.createCategory(creatorId = "cestmir", id = "movies")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        CreateCreatorPost(
                            "cestmir",
                            attributes,
                            // gaming is not one of cestmir's categories
                            setOf("sports", "gaming"),
                            isSponsored = true,
                            isAgeRestricted = true,
                            communityId = null,
                        ),
                    )
                }.withMessage("Invalid categories [gaming], creator 'cestmir' does not have these categories")
        }
    }

    @Nested
    inner class UpdateCreatorPost {
        @Test
        fun `should update creator post`() {
            val now = Instant.now()
            mockNow(now.toString())
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")
            testHelper.createCategory(creatorId = "cestmir", id = "movies")
            val post = testHelper.createPost(
                userId = "cestmir",
                text = "old text",
                categories = listOf("sports"),
                textHtml = "<p>old text</p>",
                updatedAt = now,
                excludedFromRss = false,
            )

            val attributes = PostInput(
                "new text",
                "<p>new text</p>",
                listOf(),
                textDelta = "delta",
            )
            val result = underTest.execute(
                UpdateCreatorPost(
                    userId = "cestmir",
                    postId = post.id,
                    attributes = attributes,
                    categories = setOf("movies"),
                    publishedAt = null,
                    pinnedAt = now + 10.seconds,
                    isAgeRestricted = true,
                    isSponsored = true,
                    excludeFromRss = true,
                ),
            )

            assertThat(result).isEqualTo(TestCollections.postsCollection[result.id].get())

            assertThat(result).isEqualTo(
                post.copy(
                    text = "new text",
                    textHtml = "<p>new text</p>",
                    textDelta = "delta",
                    categories = listOf("movies"),
                    pinnedAt = now + 10.seconds,
                    isSponsored = true,
                    isAgeRestricted = true,
                    excludeFromRss = true,
                ),
            )
        }

        @Test
        fun `publishedAt cannot be more than 5 minutes in the past`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            val post = testHelper.createPost("cestmir")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(
                    UpdateCreatorPost(
                        userId = "cestmir",
                        postId = post.id,
                        attributes = attributes,
                        categories = setOf(),
                        publishedAt = Instant.now() - 6.minutes,
                        isAgeRestricted = true,
                        isSponsored = true,
                        excludeFromRss = true,
                    ),
                )
            }
        }

        @Test
        fun `categories that are not defined by the creator should not be allowed`() {
            val underTest = prepareService()
            testHelper.createUser("cestmir")
            val post = testHelper.createPost("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")
            testHelper.createCategory(creatorId = "cestmir", id = "movies")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCreatorPost(
                            userId = "cestmir",
                            postId = post.id,
                            attributes = attributes,
                            // gaming is not one of cestmir's categories
                            categories = setOf("sports", "gaming"),
                            isAgeRestricted = true,
                            isSponsored = true,
                            excludeFromRss = false,
                        ),
                    )
                }.withMessage("Invalid categories [gaming], creator 'cestmir' does not have these categories")
        }

        @Test
        fun `creator cannot pin more than 3 posts on his profile`() {
            val underTest = prepareService()

            val post = testHelper.createPost("cestmir")
            (1..3).forEach { _ ->
                testHelper.createPost("cestmir", pinnedAt = Instant.now())
            }

            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCreatorPost(
                            userId = "cestmir",
                            postId = post.id,
                            attributes = PostInput("Textik", "<p>Textik</p>", listOf()),
                            categories = emptySet(),
                            isAgeRestricted = true,
                            isSponsored = true,
                            excludeFromRss = false,
                            pinnedAt = Instant.now(),
                        ),
                    )
                }
                .withMessage("Only three pinned posts are allowed.")
        }

        @Test
        fun `community owner cannot pin more than 3 posts in a community`() {
            val underTest = prepareService()

            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            val thread = testHelper.createPost("cestmir", communityId = community.id)
            (1..3).forEach { _ ->
                testHelper.createPost("cestmir", pinnedAt = Instant.now(), communityId = community.id)
            }

            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCreatorPost(
                            userId = "cestmir",
                            postId = thread.id,
                            attributes = PostInput("Textik", "<p>Textik</p>", listOf()),
                            categories = emptySet(),
                            isAgeRestricted = true,
                            isSponsored = true,
                            excludeFromRss = false,
                            pinnedAt = Instant.now(),
                        ),
                    )
                }
                .withMessage("Only three pinned posts are allowed.")
        }

        @Test
        fun `community owner can pin any thread`() {
            val underTest = prepareService()

            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            val thread = testHelper.createPost("member", communityId = community.id)

            val pinnedAt = Instant.now().truncated()
            val result = underTest.execute(
                UpdateCreatorPost(
                    userId = "cestmir",
                    postId = thread.id,
                    attributes = PostInput("Textik", "<p>Textik</p>", listOf()),
                    categories = emptySet(),
                    isAgeRestricted = true,
                    isSponsored = true,
                    excludeFromRss = false,
                    pinnedAt = pinnedAt,
                ),
            )

            assertThat(result.pinnedAt).isEqualTo(pinnedAt)
        }

        @Test
        fun `only community owner can edit other members threads`() {
            val underTest = prepareService()

            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            val thread = testHelper.createPost("cestmir", communityId = community.id)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCreatorPost(
                            userId = "another-member",
                            postId = thread.id,
                            attributes = PostInput("Textik", "<p>Textik</p>", listOf()),
                            categories = emptySet(),
                            isAgeRestricted = true,
                            isSponsored = true,
                            excludeFromRss = false,
                            pinnedAt = Instant.now(),
                        ),
                    )
                }
                .withMessage("Only community owner can edit other user's threads")
        }

        @Test
        fun `members cannot edit others members threads`() {
            val underTest = prepareService()

            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            val thread = testHelper.createPost("cestmir", communityId = community.id)

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCreatorPost(
                            userId = "another-member",
                            postId = thread.id,
                            attributes = PostInput("Textik", "<p>Textik</p>", listOf()),
                            categories = emptySet(),
                            isAgeRestricted = true,
                            isSponsored = true,
                            excludeFromRss = false,
                            pinnedAt = null,
                        ),
                    )
                }
                .withMessage("Only community owner can edit other user's threads")
        }
    }

    private fun prepareService(
        testClock: Clock = TestClock(Instant.ofEpochSecond(1755450161)),
    ): CreatorPostCommandService {
        val gjirafaServiceMock = mockk<GjirafaUploadsService>()
        val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
        return CreatorPostCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
            TestRepositories.communityRepository,
            PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
                testClock,
            ),
            TestCollections.categoriesCollection,
            TestEnvironmentVariables,
            TestLogger,
        )
    }
}
