package hero.api.post.controller.dto

import hero.api.post.service.CommentData
import hero.api.post.service.ReplyData
import hero.baseutils.minusDays
import hero.baseutils.plusDays
import hero.model.Post
import hero.model.PostCounts
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriberType
import hero.model.topics.PostState
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class PostDtoMappersKtTest {
    @Nested
    inner class PostDataToResponse {
        @Test
        fun `post is correctly anonymized`() {
            val post = post(userId = "cestmir")

            val response = post.toResponse(
                renderMeta = PostRenderMeta(
                    fullResponse = true,
                    paymentsFromUserIds = null,
                    showText = true,
                    isAuthor = true,
                    previewEnabled = false,
                    anonymize = true,
                ),
                myVote = null,
            )

            assertThat(response.text).isNotEqualTo(post.text)
            assertThat(response.textHtml).isNotEqualTo(post.textHtml)
            assertThat(response.textDelta).isNotEqualTo(post.textDelta)
            assertThat(response.text).isEqualTo("efficitur")
            assertThat(response.textHtml).isEqualTo("pellentesque")
            assertThat(response.textDelta).isEqualTo("inceptos")
        }
    }

    @Nested
    inner class CommentDataToResponse {
        @Test
        fun `post creator always has full access to all comments`() {
            val comment = CommentData(comment = post(userId = "petr"), rootPost = post(userId = "cest"), null, 0, null)

            val response = comment.toResponse("cest")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does has full access to root post if he has active subscription`() {
            val subscriber = subscriber("petr", "cest", SubscriberStatus.ACTIVE, expiresAt = Instant.now().plusDays(1))
            val comment = CommentData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                subscriber,
                0,
                null,
            )

            val response = comment.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does does not have full access to root post if his subscription expired`() {
            val subscriber = subscriber("petr", "cest", SubscriberStatus.UNPAID, expiresAt = Instant.now().minusDays(1))
            val comment = CommentData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                subscriber,
                0,
                null,
            )

            val response = comment.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isFalse()
        }

        @Test
        fun `user does not have full access to root post if is not a subscriber`() {
            val comment = CommentData(comment = post(userId = "petr"), rootPost = post(userId = "cest"), null, 0, null)

            val response = comment.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isFalse()
        }
    }

    @Nested
    inner class ReplyDataToResponse {
        @Test
        fun `post creator always has full access to all comments`() {
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                parent = post(userId = "filip"),
                subscriptionInfo = null,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("cest")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does has full access to root post if he has active subscription`() {
            val subscriber = subscriber("petr", "cest", SubscriberStatus.ACTIVE, expiresAt = Instant.now().plusDays(1))
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                parent = post(userId = "filip"),
                subscriptionInfo = subscriber,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does does not have full access to root post if his subscription expired`() {
            val subscriber = subscriber("filip", "ces", SubscriberStatus.UNPAID, expiresAt = Instant.now().minusDays(1))
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "ces"),
                parent = post(userId = "filip"),
                subscriptionInfo = subscriber,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("filip")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does not have full access to root post if is not a subscriber`() {
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "ces"),
                parent = post(userId = "filip"),
                subscriptionInfo = null,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isTrue()
        }
    }
}

// TODO move this to some test module, since this does not belong to integration testing module
private fun post(
    id: String = UUID.randomUUID().toString(),
    userId: String = "user-id",
    parentId: String = "parent-id",
) = Post(
    id = id,
    text = "text",
    textHtml = "text-html",
    textDelta = "text-delta",
    state = PostState.PUBLISHED,
    userId = userId,
    parentId = parentId,
    siblingId = "sibling-id",
    parentUserId = "parent-user-id",
    published = Instant.now(),
    pinnedAt = Instant.now(),
    messageThreadId = null,
    assets = listOf(),
    price = null,
    counts = PostCounts(),
    categories = listOf(),
    created = Instant.now(),
    updated = Instant.now(),
)

private fun subscriber(
    userId: String,
    creatorId: String,
    status: SubscriberStatus = SubscriberStatus.ACTIVE,
    expiresAt: Instant = Instant.now(),
) = Subscriber(
    userId = userId,
    creatorId = creatorId,
    couponMethod = null,
    status = status,
    subscribed = Instant.now(),
    expires = expiresAt,
    subscriberType = SubscriberType.STRIPE,
    tierId = "EUR05",
)
