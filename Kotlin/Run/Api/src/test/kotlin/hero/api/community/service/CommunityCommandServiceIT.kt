package hero.api.community.service

import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.model.ImageAsset
import hero.model.topics.CommunityCreated
import hero.repository.community.CommunityRepository
import hero.sql.jooq.Tables.USER
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.time.TestClock
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class CommunityCommandServiceIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(1755450161)

    @Nested
    inner class CreateCommunity {
        @Test
        fun `should create a community for a user who can create communities`() {
            val communityRepository = CommunityRepository(testContext)
            val underTest = prepareService()

            val user = testHelper.createUser("filip", canCreateCommunity = true)

            val community = underTest.execute(CreateCommunity("filip"))

            with(communityRepository.getById(community.id)) {
                assertThat(this).isEqualTo(community)
                assertThat(name).isEqualTo(user.name)
                assertThat(description).isEqualTo("Community")
                assertThat(slug).isEqualTo(user.path)
                assertThat(ownerId).isEqualTo("filip")
                assertThat(membersCount).isEqualTo(1)
                assertThat(image).isEqualTo(user.image)
                assertThat(createdAt).isEqualTo(expectedTimestamp)
                assertThat(updatedAt).isEqualTo(expectedTimestamp)
                assertThat(deletedAt).isNull()
            }

            val publishedEvent = slot<CommunityCreated>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.communityId).isEqualTo(community.id)

            assertThat(testContext.selectFrom(USER).where(USER.ID.eq("filip")).fetchSingle().ownedCommunitiesCount)
                .isEqualTo(1)
        }

        @Test
        @Disabled
        fun `user cannot create a community if they are not allowed to create communities`() {
            val underTest = prepareService()

            testHelper.createUser("filip", canCreateCommunity = false)

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(CreateCommunity("filip"))
            }.withMessage("User filip cannot create communities")
        }

        @Test
        fun `user cannot create a community if they already have one`() {
            val underTest = prepareService()

            testHelper.createUser("filip", canCreateCommunity = true)

            // Create first community
            underTest.execute(CreateCommunity("filip"))

            // Try to create second community
            assertThatExceptionOfType(ConflictException::class.java).isThrownBy {
                underTest.execute(CreateCommunity("filip"))
            }.withMessage("User filip already has a community")
        }
    }

    @Nested
    inner class UpdateCommunity {
        @Test
        fun `should update community with given attributes`() {
            val communityRepository = CommunityRepository(testContext)
            val underTest = prepareService()

            testHelper.createUser("owner", canCreateCommunity = true)
            val community = testHelper.createCommunity("owner")

            val newImage = ImageAsset(
                id = "https://example.com/new-image.jpg",
                width = 800,
                height = 600,
                fileName = null,
            )

            val updatedCommunity = underTest.execute(
                UpdateCommunity(
                    communityId = community.id,
                    userId = "owner",
                    name = "Updated Community Name",
                    description = "Updated community description",
                    image = newImage,
                    slug = "updated-slug",
                ),
            )

            with(communityRepository.getById(community.id)) {
                assertThat(this).isEqualTo(updatedCommunity)
                assertThat(name).isEqualTo("Updated Community Name")
                assertThat(description).isEqualTo("Updated community description")
                assertThat(image).isEqualTo(newImage)
                assertThat(slug).isEqualTo("updated-slug")
                assertThat(ownerId).isEqualTo("owner")
                assertThat(membersCount).isEqualTo(community.membersCount)
                assertThat(threadsCount).isEqualTo(community.threadsCount)
                assertThat(createdAt).isEqualTo(community.createdAt)
                assertThat(updatedAt).isEqualTo(expectedTimestamp)
                assertThat(deletedAt).isNull()
            }
        }

        @Test
        fun `user cannot update community they do not own`() {
            val underTest = prepareService()
            testHelper.createUser("owner", canCreateCommunity = true)
            testHelper.createUser("other", canCreateCommunity = true)
            val community = testHelper.createCommunity("owner")

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "other",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "hacked-slug",
                        ),
                    )
                }
                .withMessage("User other cannot update community ${community.id}")
        }

        @Test
        fun `should throw conflict exception if another community has given slug`() {
            val underTest = prepareService()
            testHelper.createUser("owner", canCreateCommunity = true)
            testHelper.createUser("other", canCreateCommunity = true)
            val community = testHelper.createCommunity("owner", slug = "slug-1")
            testHelper.createCommunity("other", slug = "slug-2")

            assertThatExceptionOfType(ConflictException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "owner",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "slug-2",
                        ),
                    )
                }
                .withMessage("Community with slug slug-2 already exists")
        }
    }

    private fun prepareService(): CommunityCommandService {
        val communityRepository = CommunityRepository(testContext)
        return CommunityCommandService(
            communityRepository = communityRepository,
            userRepository = TestRepositories.userRepository,
            pubSub = pubSubMock,
            lazyContext = lazyTestContext,
            clock = TestClock(expectedTimestamp),
        )
    }
}
