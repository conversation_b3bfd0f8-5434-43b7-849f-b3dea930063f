package hero.api.community.service

import hero.exceptions.http.NotFoundException
import hero.repository.community.CommunityRepository
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import java.util.UUID

class CommunityQueryServiceIT : IntegrationTest() {
    @Test
    fun `should get community by id`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        testHelper.createUser("owner-id", canCreateCommunity = true)
        val community = testHelper.createCommunity(
            ownerId = "owner-id",
            name = "Test Community",
            description = "Test Description",
            slug = "test-community",
        )

        val result = underTest.execute(GetCommunity(community.id, null, null))

        with(result.community) {
            assertThat(this).isEqualTo(community)
            assertThat(id).isEqualTo(community.id)
            assertThat(name).isEqualTo("Test Community")
            assertThat(description).isEqualTo("Test Description")
            assertThat(slug).isEqualTo("test-community")
            assertThat(ownerId).isEqualTo("owner-id")
        }

        assertThat(result.isMember).isFalse()
    }

    @Test
    fun `should get community by slug`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        testHelper.createUser("owner-id", canCreateCommunity = true)
        val community = testHelper.createCommunity(
            ownerId = "owner-id",
            name = "Test Community",
            description = "Test Description",
            slug = "test-community-slug",
        )

        val result = underTest.execute(GetCommunity(null, "test-community-slug", null))

        with(result.community) {
            assertThat(this).isEqualTo(community)
            assertThat(name).isEqualTo("Test Community")
            assertThat(description).isEqualTo("Test Description")
            assertThat(slug).isEqualTo("test-community-slug")
            assertThat(ownerId).isEqualTo("owner-id")
        }
    }

    @Test
    fun `owner is always part of community`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        testHelper.createUser("owner-id", canCreateCommunity = true)
        val community = testHelper.createCommunity(
            ownerId = "owner-id",
            name = "Test Community",
            description = "Test Description",
            slug = "test-community-slug",
        )

        val result = underTest.execute(GetCommunity(null, "test-community-slug", "owner-id"))

        with(result) {
            assertThat(this.community).isEqualTo(community)
            assertThat(this.isMember).isTrue
        }
    }

    @Test
    fun `member flag is true for non owner member`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        testHelper.createUser("owner-id", canCreateCommunity = true)
        val community = testHelper.createCommunity(
            ownerId = "owner-id",
            name = "Test Community",
            description = "Test Description",
            slug = "test-community-slug",
        )

        testHelper.createUser("member-id")
        testHelper.createCommunityMember(community.id, "member-id")

        val result = underTest.execute(GetCommunity(null, "test-community-slug", "member-id"))

        with(result) {
            assertThat(this.community).isEqualTo(community.copy(membersCount = 1))
            assertThat(this.isMember).isTrue
        }
    }

    @Test
    fun `member flag is false for non member`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        testHelper.createUser("owner-id", canCreateCommunity = true)
        val community = testHelper.createCommunity(
            ownerId = "owner-id",
            name = "Test Community",
            description = "Test Description",
            slug = "test-community-slug",
        )

        val result = underTest.execute(GetCommunity(null, "test-community-slug", "non-member-id"))

        with(result) {
            assertThat(this.community).isEqualTo(community)
            assertThat(this.isMember).isFalse
        }
    }

    @Test
    fun `should throw NotFoundException when community does not exist by id`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        val nonExistentId = UUID.randomUUID()

        assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
            underTest.execute(GetCommunity(nonExistentId, null, null))
        }
    }

    @Test
    fun `should throw NotFoundException when community does not exist by slug`() {
        val communityRepository = CommunityRepository(testContext)
        val underTest = CommunityQueryService(
            lazyContext = lazyTestContext,
            communityRepository = communityRepository,
        )

        assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
            underTest.execute(GetCommunity(null, "non-existent-slug", null))
        }
    }
}
