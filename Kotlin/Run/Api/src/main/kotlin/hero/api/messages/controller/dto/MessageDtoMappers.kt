package hero.api.messages.controller.dto

import hero.api.messages.service.MessageThreadWithLastMessage
import hero.api.messages.service.MessageThreadWithPermissions
import hero.api.messages.service.MessageWithPayment
import hero.api.post.controller.dto.PostRenderMeta
import hero.api.post.controller.dto.toResponse
import hero.api.user.controller.dto.toResponse
import hero.contract.api.dto.PostResponse

/**
 * Right now used only to see threads list (like in messenger), where there is only a text preview for last message,
 * therefore we should never return full assets. In case we will also add image previews, Post entity will have to
 * be replaced with a DTO enriched with payment details.
 */
fun MessageThreadWithLastMessage.toResponse(userId: String): MessageThreadResponse {
    val messageThread = this.messageThread
    return MessageThreadResponse(
        id = messageThread.id,
        participants = participants.map { it.toResponse(listOf()) },
        participantIds = messageThread.userIds.toSet(),
        createdAt = messageThread.createdAt,
        seenAt = messageThread.seens[userId],
        checkedAt = messageThread.checks[userId],
        lastMessageAt = messageThread.lastMessageAt,
        deleted = userId in messageThread.deletedFor,
        archived = userId in messageThread.archivedFor,
        deletedAt = messageThread.deletes[userId],
        canMessage = messageThread.canMessage.getValue(userId),
        lastMessage = this.lastMessage.toResponse(PostRenderMeta(fullResponse = false, showText = true), 0),
    )
}

fun MessageWithPayment.toResponse(
    requesterId: String,
    isImpersonation: Boolean,
): PostResponse {
    val isAuthorOfTheMessage = message.userId == requesterId
    val messageIsFree = (message.price ?: 0) == 0L
    val paidForTheMessage = payment != null
    val fullResponse = isAuthorOfTheMessage || messageIsFree || paidForTheMessage

    val renderMeta = PostRenderMeta(fullResponse, null, true, anonymize = isImpersonation)
    return message.toResponse(renderMeta, 0)
}

fun MessageThreadWithPermissions.toResponse(userId: String): MessageThreadDetailsResponse =
    MessageThreadDetailsResponse(
        id = messageThread.id,
        participants = participants.map { it.toResponse(listOf()) },
        participantIds = messageThread.userIds,
        createdAt = messageThread.createdAt,
        canPost = permissions.canPost,
        relation = permissions.relation,
        commonCreators = permissions.creators,
        seenAt = messageThread.seens[userId],
        checkedAt = messageThread.checks[userId],
        lastMessageAt = messageThread.lastMessageAt,
        lastMessageId = messageThread.lastMessageId,
        deleted = userId in messageThread.deletedFor,
        archived = userId in messageThread.archivedFor,
        deletedAt = messageThread.deletes[userId],
    )
