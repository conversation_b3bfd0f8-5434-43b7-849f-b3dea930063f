package hero.api.user.repository

import com.github.kittinunf.fuel.httpPost
import com.google.cloud.firestore.Query
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.baseutils.plusHours
import hero.baseutils.retryOn
import hero.baseutils.serviceCall
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.FirestoreFulltextService
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.imageProxy
import hero.gcloud.isTrue
import hero.gcloud.root
import hero.gcloud.union
import hero.gcloud.where
import hero.http4k.auth.parseJwtUser
import hero.jackson.toJson
import hero.jwt.ACCESS_TOKEN
import hero.jwt.JwtUser
import hero.jwt.toJwt
import hero.model.CategoryDto
import hero.model.CategoryDtoRelationship
import hero.model.Creator
import hero.model.DiscordDtoAttributes
import hero.model.DiscordMeta
import hero.model.ExtractedUser
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.ImageAsset
import hero.model.OAuthProvider
import hero.model.Path
import hero.model.Role
import hero.model.SpotifyMeta
import hero.model.StorageEntityType
import hero.model.StorageUploadFromUrlPostBody
import hero.model.StorageUploadResponse
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SupportCounts
import hero.model.TierDtoRelationship
import hero.model.User
import hero.model.UserDto
import hero.model.UserDtoAttributes
import hero.model.UserDtoRelationships
import hero.model.UserStateChange
import hero.model.UserStateChanged
import hero.model.UserStatus
import hero.repository.user.UserRepository
import hero.stripe.service.StripeAccountService
import org.http4k.core.Request
import java.time.Instant
import kotlin.concurrent.thread
import kotlin.math.min

@Deprecated("Do not add anything, is deprecated and will be rewritten, soonish")
class UsersRepository(
    private val firestoreFulltext: FirestoreFulltextService,
    private val imageRepository: ImageRepository,
    private val pathCollection: TypedCollectionReference<Path>,
    private val pubSub: PubSub,
    private val subscriberCollection: TypedCollectionReference<Subscriber>,
    private val accountService: StripeAccountService,
    val collection: TypedCollectionReference<User>,
    private val userRepository: UserRepository,
    private val userIdGenerator: UserIdGenerator = UserIdGenerator(),
) {
    fun factory(extractedUser: ExtractedUser): Pair<User, UserStateChange> {
        val lowercaseEmail = extractedUser.email?.trim()?.lowercase()
        val facebookId = extractedUser.meta["facebookId"]?.let { it as? String }
        val dbUser =
            // only when connecting already existing user to a new provider
            extractedUser.userId?.let { collection[it].fetch() }
                // first search by facebookId if exists
                ?: facebookId?.let {
                    collection
                        .where(User::status).isNotEqualTo(UserStatus.DELETED)
                        .and(User::facebookId).isEqualTo(it)
                        .fetchSingle()
                }
                // then search by oAuthId field (e.g. facebookId) - if provided (eg. apple and email don't have such)
                ?: collection
                    .where(User::status).isNotEqualTo(UserStatus.DELETED)
                    .and(User::firebaseId).isEqualTo(extractedUser.id)
                    .fetchSingle()
                // then search by its email (if given)
                ?: lowercaseEmail?.let {
                    collection
                        .where(User::status).isNotEqualTo(UserStatus.DELETED)
                        .and(User::email).isEqualTo(it).fetchSingle()
                }
        val isNewUser = dbUser == null

        // or create a new user
        val user: User = if (dbUser != null) {
            dbUser
        } else {
            val id = generateUserIdVerified(extractedUser.name)
            val userPath = userIdGenerator.generatePath(extractedUser.name)
            val extractedLanguage = extractedUser.language.nullIfEmpty() ?: "en"
            log.info("New user '$id' just signed up.", mapOf("userId" to id))

            User(
                id = id,
                path = userPath + id,
                creator = Creator(FREE_SUBSCRIBER_TIER_ID),
                name = extractedUser.name,
                email = lowercaseEmail,
                affiliateSource = extractedUser.affiliateSource,
                // TODO better handle default language
                language = extractedLanguage,
                canCreateCommunity = SystemEnv.environment == "devel",
            )
        }

        if (user.image?.id == null && extractedUser.imageUrl != null) {
            try {
                // retryOn<IOException> might not be enough if the inner call fails, we need to use general Exception
                retryOn(Exception::class) {
                    // temporary JWT for image upload, valid only for 60 seconds
                    val tempJwt = JwtUser(user.id, Instant.now().epochSecond + 60, 0).toJwt()
                    val response = serviceCall("media", "/v1/uploads-from-url")
                        .httpPost()
                        .header("Cookie", "$ACCESS_TOKEN=$tempJwt")
                        .body(StorageUploadFromUrlPostBody(StorageEntityType.USER, extractedUser.imageUrl!!).toJson())
                        .fetch<StorageUploadResponse>()
                    user.image = imageRepository.imageMeta(response.targetUrl, null)
                }
            } catch (e: Exception) {
                // this shouldn't be fatal for login/signup
                log.fatal(
                    "Couldn't store (non-fatal, continuing) user's image: $extractedUser",
                    mapOf("userId" to user.id),
                    cause = e,
                )
            }
        }

        if (extractedUser.provider == OAuthProvider.SPOTIFY) {
            user.spotify = SpotifyMeta(
                extractedUser.id,
                extractedUser.accessToken!!,
                extractedUser.refreshToken!!,
                extractedUser.tokenExpiresAt!!,
            )
        }

        if (extractedUser.provider == OAuthProvider.DISCORD) {
            if (user.discord == null) {
                user.discord = DiscordMeta()
            }
            user.discord!!.id = extractedUser.id
            user.discord!!.guildId = extractedUser.secondaryId
            user.discord!!.accessToken = extractedUser.accessToken
            user.discord!!.refreshToken = extractedUser.refreshToken
            user.discord!!.tokenExpiresAt = extractedUser.tokenExpiresAt
            user.discord!!.active = true
        }

        if (extractedUser.provider == OAuthProvider.FIREBASE) {
            user.firebaseId = extractedUser.id
            facebookId?.let { user.facebookId = it }
        }

        store(user, if (isNewUser) UserStateChange.CREATED else UserStateChange.PATCHED)
        return user to if (isNewUser) UserStateChange.CREATED else UserStateChange.PATCHED
    }

    internal fun generateUserIdVerified(name: String): String =
        retryOn(IllegalStateException::class, retryWaitMillis = 1L, retries = 2) {
            val id = userIdGenerator.generateId(name)
            if (find(id) != null) {
                error("Duplicate user id '$id' for a new User was generated. Will be retried.")
            }
            id
        }

    fun path(path: String): Path? = pathCollection[path].fetch()

    fun store(
        user: User,
        stateChange: UserStateChange = UserStateChange.PATCHED,
    ) {
        val path = path(user.path) ?: Path(user.path, user.id)
        pathCollection[user.path].set(path)
        userRepository.save(user)
        collection[user.id].set(user)

        if (user.creator.stripeAccountId != null) {
            thread {
                try {
                    accountService.updateAccount(user)
                } catch (e: Exception) {
                    log.fatal(
                        "Cannot update user's ${user.id} stripe account ${user.creator.stripeAccountId}: ${e.message}",
                        mapOf("userId" to user.id),
                        e,
                    )
                }
            }
        }

        // publish the changed entity to pub/sub
        pubSub.publish(
            UserStateChanged(
                stateChange,
                user,
            ),
        )
    }

    /** Stricter version of `find`, throwing exception on non-existence. */
    fun get(id: String): User {
        val user = find(id)
        if (user == null || user.status == UserStatus.DELETED) {
            throw NotFoundException("User $id was not found.", labels = mapOf("userId" to id))
        }
        return user
    }

    /** Relaxed version of `get`, returning `null` on non-existence. */
    internal fun find(id: String): User? = collection[id].fetch()

    fun get(request: Request): User = getRelaxed(request) ?: throw UnauthorizedException()

    fun getRelaxed(request: Request): User? = request.parseJwtUser()?.id?.let { get(it) }

    fun get(
        request: Request,
        pathUserId: String? = null,
    ): User {
        val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
        if (pathUserId != null && jwtUser.id != pathUserId) {
            throw ForbiddenException(
                "JWT user does not correspond to the requested user: $pathUserId != ${jwtUser.id}",
                labels = mapOf("userId" to jwtUser.id),
            )
        }
        return get(jwtUser.id)
    }

    fun getUsers(
        query: String? = null,
        path: String? = null,
        subscriptionUserId: String? = null,
        subscriptionCreatorId: String? = null,
        queryUserIds: List<String> = listOf(),
        featured: Boolean? = null,
        featuredBy: String? = null,
        spotifyUri: String? = null,
        onBehalfOfUser: JwtUser? = null,
        offset: Int,
        limit: Int,
    ): List<User> {
        val userIds: List<String> = when {
            queryUserIds.isNotEmpty() -> queryUserIds.distinct()
            featuredBy != null -> {
                collection
                    .where(User::featuredBy).contains(featuredBy)
                    .orderBy(root(User::counts).path(SupportCounts::supporters), Query.Direction.DESCENDING)
                    .offset(offset * (limit - 1))
                    .limit(limit)
                    .fetchAll()
                    .map { it.id }
                    .shuffled()
            }

            featured == true -> {
                // defined in https://gitlab.com/heroheroco/general/-/issues/185
                collection
                    .where(User::featured).isTrue()
                    .orderBy(root(User::counts).path(SupportCounts::supporters), Query.Direction.DESCENDING)
                    .offset(offset * (limit - 1))
                    .limit(limit)
                    .fetchAll()
                    .map { it.id }
                    .shuffled()
            }

            spotifyUri != null -> {
                collection.where(User::spotifyUri).isEqualTo(spotifyUri)
                    .fetchAll()
                    .map { it.id }
            }

            path != null -> {
                val userId = try {
                    pathCollection[path].fetch()?.userId
                        // fallback to search by userId
                        ?: collection.where(User::path).isEqualTo(path).fetchSingle()?.id
                        ?: collection[path].fetch()?.id
                } catch (e: IllegalArgumentException) {
                    null
                }
                listOfNotNull(userId)
            }

            query != null && ("@" in query) && onBehalfOfUser?.roleIndex == Role.MODERATOR.ordinal ->
                // if moderator uses `@` in search query, we will search directly by email
                collection
                    .where(User::email).isEqualTo(query.lowercase())
                    .and(User::status).isEqualTo(UserStatus.ACTIVE)
                    .fetchAll()
                    .map { it.id }

            query != null -> {
                // Note we prefer only "my subscriptions" as my "subscribers" would be costly
                // to compute (there might thousands of them).
                // https://gitlab.com/heroheroco/general/-/issues/303
                val preferredIds = if (onBehalfOfUser != null) {
                    subscriberCollection
                        .where(Subscriber::userId).isEqualTo(onBehalfOfUser.id)
                        // we don't care about expired subscriptions here, we still might want to prefer users which have previosly been followed
                        .fetchAll()
                        .map { it.creatorId }
                        .distinct()
                } else {
                    emptyList()
                }
                val preferredFoundUserIds = if (preferredIds.isNotEmpty()) {
                    // IN supports up to 10 comparison values
                    preferredIds.chunked(10).flatMap { chunkedIds ->
                        firestoreFulltext.search(query, chunkedIds, null, null)
                    }
                } else {
                    emptyList()
                }
                val prependedUserIds = if (offset == 0) preferredFoundUserIds else listOf()

                prependedUserIds
                    .plus(
                        firestoreFulltext
                            .search(query, null, offset, limit)
                            .minus(preferredFoundUserIds),
                    )
            }

            subscriptionUserId != null -> {
                subscriberCollection
                    .where(Subscriber::userId).isEqualTo(subscriptionUserId)
                    .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
                    .orderBy(Subscriber::subscribed, Query.Direction.DESCENDING)
                    .offset(offset * (limit - 1))
                    .limit(limit)
                    .fetchAll()
                    .map { subscribee -> subscribee.creatorId }
            }

            subscriptionCreatorId != null -> {
                subscriberCollection
                    .where(Subscriber::creatorId).isEqualTo(subscriptionCreatorId)
                    .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
                    .orderBy(Subscriber::subscribed, Query.Direction.DESCENDING)
                    .offset(offset * (limit - 1))
                    .limit(limit)
                    .fetchAll()
                    .map { subscriber -> subscriber.userId }
            }

            else -> {
                listOf()
            }
        }

        val fetchedUsers = userIds
            // firestore allows up to 10 comparison values
            .chunked(10)
            .map { chunkOf10 ->
                collection
                    .where(User::id).isIn(chunkOf10)
                    // TODO sort by order in chunks
                    .fetchAll()
            }
            .flatten()
            // when fulltext-searching, we want to get rid of deleted users
            .filter { query == null || it.status == UserStatus.ACTIVE }
            .map { it.id to it }
            .toMap()

        return userIds.mapNotNull { fetchedUsers[it] }
    }

    fun toDto(
        user: User,
        details: Boolean,
        categories: List<CategoryDto>?,
    ): UserDto =
        user.run {
            if (user.status == UserStatus.DELETED)
                UserDto(
                    id = id,
                    attributes = UserDtoAttributes(
                        name = null,
                        createdAt = null,
                        bio = null,
                        image = null,
                        path = null,
                        pathChangeableAt = null,
                        subscribable = false,
                        verified = false,
                        stripeAccountId = null,
                        counts = SupportCounts(),
                        status = UserStatus.DELETED,
                        hasDrm = false,
                        hasRssFeed = false,
                        hasSpotifyExport = false,
                        hasSpotifyConnection = false,
                        hasLivestreams = false,
                        hasGiftsAllowed = false,
                        spotifyFeedReady = false,
                        language = null,
                        discord = null,
                        creatorSuspended = null,
                        company = null,
                        privacyPolicyEffectiveAt = null,
                        email = null,
                        lastChargeFailedAt = null,
                        gjirafaLivestream = null,
                        isOfAge = false,
                    ),
                    relationships = UserDtoRelationships(
                        // TODO could this be non-nullable?
                        tier = null,
                        categories = null,
                    ),
                )
            else
                UserDto(
                    id = id,
                    attributes = UserDtoAttributes(
                        name = name,
                        createdAt = created,
                        bio = bio,
                        bioHtml = bioHtml,
                        bioEn = bioEn,
                        bioHtmlEn = bioHtmlEn,
                        image = image?.let {
                            ImageAsset(
                                it.id.imageProxy(),
                                it.width,
                                it.height,
                                it.fileName,
                                it.fileSize,
                            )
                        },
                        path = path,
                        pathChangeableAt = pathChanged.pathUpdateableAfter(),
                        subscribable = creator.active,
                        verified = creator.verified,
                        // We consider the profile usable if the user finished onboarding. In such case, frontend should
                        // use dashboard-links instead of connection-links to navigate user to Stripe.
                        stripeAccountId = if (creator.stripeAccountOnboarded) creator.stripeAccountId else null,
                        counts = SupportCounts(
                            supporting = counts.supporting,
                            supporters = if (details) {
                                counts.supporters
                            } else {
                                min(counts.supporters, counts.supportersThreshold ?: Long.MAX_VALUE)
                            },
                            supportersThreshold = if (details) null else counts.supportersThreshold,
                            posts = counts.posts,
                            incomes = if (details) counts.incomes else null,
                            incomesClean = if (details) counts.incomesClean else null,
                            payments = if (details) counts.payments else null,
                            invoices = if (details) counts.invoices else null,
                        ),
                        status = status,
                        hasDrm = hasDrm ?: false,
                        hasRssFeed = hasRssFeed,
                        hasSpotifyExport = hasSpotifyExport,
                        hasSpotifyConnection = spotify != null,
                        hasGiftsAllowed = hasGiftsAllowed,
                        hasLivestreams = hasLivestreams,
                        spotifyUri = spotifyUri,
                        spotifyFeedReady = spotifyFeedReady == true,
                        language = language,
                        discord = user.discord?.let {
                            if (it.active) DiscordDtoAttributes(id = it.id!!, guildId = it.guildId) else null
                        },
                        notificationsEnabled = if (details) user.notificationsEnabled else null,
                        creatorSuspended = user.creator.suspended,
                        analytics = analytics,
                        company = if (details) company else null,
                        privacyPolicyEffectiveAt = privacyPolicyEffectiveAt,
                        // This `email` field is published only for `privacy-policy-generator`,
                        // otherwise it is read by creators from the `/details` endpoint. That
                        // is distinguished by the `privacyPolicyEffectiveAt`.
                        email = if (details && privacyPolicyEffectiveAt != null) {
                            user.creator.emailPublic ?: user.email
                        } else {
                            null
                        },
                        emailVerified = if (details) user.isEmailVerifiedOrThirdParty else null,
                        lastChargeFailedAt = if (details) lastChargeFailedAt else null,
                        gjirafaLivestream = if (details) gjirafaLivestream else null,
                        isOfAge = if (details) isOfAge else null,
                        hasPostPreviews = hasPostPreviews,
                    ),
                    relationships = UserDtoRelationships(
                        tier = TierDtoRelationship(creator.tierId),
                        categories = categories?.map { CategoryDtoRelationship(it.id!!) },
                    ),
                )
        }

    fun exists(email: String): Boolean =
        collection
            .where(User::email).isEqualTo(email)
            .and(User::status).isEqualTo(UserStatus.ACTIVE)
            .fetchSingle() != null

    fun deleteStripeAccountFromUser(
        userId: String,
        oldStripeAccountId: String,
    ) {
        val reference = collection[userId]
        reference.field(root(User::creator).path(Creator::stripeAccountId)).update(null)
        reference.field(root(User::creator).path(Creator::stripeAccountLegacyIds)).union(oldStripeAccountId)
        reference.field(root(User::creator).path(Creator::stripeAccountOnboarded)).update(false)
        reference.field(root(User::creator).path(Creator::stripeAccountActive)).update(false)
        reference.field(root(User::creator).path(Creator::active)).update(false)
    }
}

fun Instant.pathUpdateableAfter() = this.plusHours(1)
