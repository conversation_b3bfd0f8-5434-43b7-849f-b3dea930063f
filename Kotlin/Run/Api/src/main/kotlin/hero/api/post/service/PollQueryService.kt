package hero.api.post.service

import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.model.Poll
import hero.model.PollOptionId
import hero.model.topics.PostState
import hero.repository.post.JooqPollHelper
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.POLL
import hero.sql.jooq.Tables.POLL_OPTION
import hero.sql.jooq.Tables.POLL_OPTION_VOTE
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.USER
import org.jooq.DSLContext
import org.jooq.impl.DSL

class PollQueryService(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun execute(query: GetPoll): PollWithVotes {
        val pollRecord = context
            .select(JooqPollHelper.pollFields)
            .from(POLL)
            .where(POLL.ID.eq(query.pollId))
            .fetchOne()

        if (pollRecord == null) {
            throw NotFoundException("Poll ${query.pollId} not found")
        }

        val creatorId = pollRecord[POLL.USER_ID]

        val userId = query.userId
        if (userId != creatorId) {
            val activeSubscription = DSL.field(
                DSL
                    .select(Tables.SUBSCRIPTION.STRIPE_ID)
                    .from(Tables.SUBSCRIPTION)
                    .where(Tables.SUBSCRIPTION.USER_ID.eq(userId).and(Tables.SUBSCRIPTION.CREATOR_ID.eq(creatorId)))
                    .and(JooqSubscriptionHelper.activeSubscription)
                    .limit(1),
            )

            val hasPostPreviews = DSL.field(
                DSL
                    .select(USER.HAS_POST_PREVIEWS)
                    .from(USER)
                    .where(USER.ID.eq(creatorId)),
            )

            val hasPreview = DSL.field(
                DSL.select(POST.HAS_PREVIEW)
                    .from(POST)
                    .where(POST.POLL_ID.eq(query.pollId))
                    .and(POST.STATE.eq(PostState.PUBLISHED.name))
                    .and(POST.USER_ID.eq(creatorId))
                    .limit(1),
            )

            val fetchResult = context.select(activeSubscription, hasPostPreviews, hasPreview).fetchSingle()

            val hasActiveSubscription = fetchResult[activeSubscription] != null
            val canBePreviewed = (fetchResult[hasPreview] ?: false) && fetchResult[hasPostPreviews]
            if (!hasActiveSubscription && !canBePreviewed) {
                throw ForbiddenException("User $userId does not subscribe $creatorId")
            }
        }

        val votes = context
            .select(POLL_OPTION_VOTE.POLL_OPTION_ID)
            .from(POLL_OPTION)
            .join(POLL_OPTION_VOTE).on(POLL_OPTION_VOTE.POLL_OPTION_ID.eq(POLL_OPTION.ID))
            .where(POLL_OPTION.POLL_ID.eq(query.pollId))
            .and(POLL_OPTION_VOTE.USER_ID.eq(query.userId))
            .map { it[POLL_OPTION_VOTE.POLL_OPTION_ID] }

        return PollWithVotes(JooqPollHelper.mapRecordToEntity(pollRecord), votes)
    }
}

data class GetPoll(val userId: String, val pollId: String)

data class PollWithVotes(
    val poll: Poll,
    val votes: List<PollOptionId>,
)
