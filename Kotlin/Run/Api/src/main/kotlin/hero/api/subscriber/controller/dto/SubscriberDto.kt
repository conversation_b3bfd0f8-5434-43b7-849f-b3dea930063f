package hero.api.subscriber.controller.dto

import hero.api.user.controller.dto.UserResponse
import hero.core.data.SimplePageResponse
import hero.model.CouponMethod
import hero.model.Currency
import hero.model.SubscriberType
import hero.model.SubscriptionsDtoStatus
import java.time.Instant

data class SubscriptionDetailsResponse(
    val status: SubscriptionsDtoStatus,
    val cancelAtPeriodEnd: Boolean,
    val expires: Instant?,
    val couponAppliedForMonths: Long?,
    val couponAppliedForDays: Long?,
    val couponExpiresAt: Instant?,
    val tierId: String?,
    val type: SubscriberType,
    val couponMethod: CouponMethod?,
    val couponPercentOff: Long?,
    val isApple: Boolean,
)

data class SubscriptionResponse(
    val id: String,
    val subscribedAt: Instant,
    val details: SubscriptionDetailsResponse?,
    val subscriber: UserResponse,
    val creator: UserResponse,
    val tier: TierResponse?,
)

data class TierResponse(
    val id: String,
    val priceCents: Int,
    val currency: Currency,
    val default: Boolean = false,
    val hidden: Boolean = false,
)

data class UpdateSubscribeRequest(
    val type: UpdateSubscribeRequestType,
)

enum class UpdateSubscribeRequestType {
    ACCEPT,
    DECLINE,
    DELETE,
}

data class PagedSubscriptionResponse(
    override val content: List<SubscriptionResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<SubscriptionResponse>

data class SubscribeRequestResponse(
    val id: Long,
    val userId: String,
    val creatorId: String,
    val createdAt: Instant,
    val acceptedAt: Instant?,
    val declinedAt: Instant?,
    val deletedAt: Instant?,
    val seenAt: Instant?,
)

data class GetSubscribeRequestResponse(
    val data: SubscribeRequestResponse?,
)

data class PagedSubscribeRequestResponse(
    override val content: List<SubscribeRequestResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<SubscribeRequestResponse>

data class CreateSubscribeRequestRequest(
    val creatorId: String,
)
