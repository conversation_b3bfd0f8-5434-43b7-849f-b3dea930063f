package hero.api.subscriber.service

import com.google.firebase.messaging.AndroidConfig
import com.google.firebase.messaging.AndroidNotification
import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.ApsAlert
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.baseutils.log
import hero.baseutils.systemEnvRelaxed
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.increment
import hero.gcloud.root
import hero.messaging.sendMulticastMessage
import hero.messaging.setImageScaled
import hero.model.CZ_VAT_COUNTRY
import hero.model.Currency
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Notification
import hero.model.NotificationType
import hero.model.StorageEntityType
import hero.model.SubscribeRequest
import hero.model.SupportCounts
import hero.model.Tier
import hero.model.User
import hero.repository.device.fetchFirebaseRegistrationTokens
import hero.repository.notification.NotificationRepository
import hero.repository.subscription.JooqSubscriptionHelper
import hero.repository.subscription.SubscribeRequestRepository
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.Subscription.SUBSCRIPTION
import hero.stripe.service.StripeSubscriptionService
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import com.google.firebase.messaging.Notification as FirebaseNotification

class SubscribeRequestCommandService(
    private val subscribeRequestRepository: SubscribeRequestRepository,
    private val usersCollection: TypedCollectionReference<User>,
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val stripeSubscriptionService: StripeSubscriptionService,
    private val notificationRepository: NotificationRepository,
    private val firebaseMessaging: FirebaseMessaging,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: RequestToSubscribe): SubscribeRequest {
        if (command.userId == command.creatorId) {
            throw BadRequestException("User cannot request himself")
        }
        val previousRequest = subscribeRequestRepository.findSingle {
            this
                .where(Tables.SUBSCRIBE_REQUEST.USER_ID.eq(command.userId))
                .and(Tables.SUBSCRIBE_REQUEST.CREATOR_ID.eq(command.creatorId))
                .and(Tables.SUBSCRIBE_REQUEST.ACCEPTED_AT.isNull)
                .and(Tables.SUBSCRIBE_REQUEST.DECLINED_AT.isNull)
                .and(Tables.SUBSCRIBE_REQUEST.DELETED_AT.isNull)
        }

        if (previousRequest != null) {
            throw ConflictException(
                "User ${command.userId} has already requested to subscribe to creator ${command.creatorId}",
            )
        }

        val subscription = context.selectFrom(SUBSCRIPTION)
            .where(SUBSCRIPTION.USER_ID.eq(command.userId).and(SUBSCRIPTION.CREATOR_ID.eq(command.creatorId)))
            .and(JooqSubscriptionHelper.activeSubscription)
            .fetch()
        if (subscription.isNotEmpty) {
            throw ConflictException("User ${command.userId} already subscribes to creator ${command.creatorId}")
        }

        val request = SubscribeRequest(
            userId = command.userId,
            creatorId = command.creatorId,
            createdAt = Instant.now(clock),
            acceptedAt = null,
            declinedAt = null,
            deletedAt = null,
            seenAt = null,
        ).run {
            usersCollection[command.creatorId].field(root(User::counts).path(SupportCounts::pendingRequests))
                .increment(1)

            val request = subscribeRequestRepository.save(this)

            val privateProfilesEnabled = systemEnvRelaxed("FF_PRIVATE_PROFILES_ENABLED") == "true"
            if (!privateProfilesEnabled) {
                return@run request
            }
            val tokens = fetchFirebaseRegistrationTokens(context, command.creatorId)
            if (tokens.isNotEmpty()) {
                val key = "push_notification_new_subscribe_request_body"
                val user = usersCollection[command.userId].get()
                val apsAlert = ApsAlert.builder().setLocalizationKey(key).addLocalizationArg(user.name).build()
                val aps = Aps.builder().setAlert(apsAlert).build()
                val apnsConfig = ApnsConfig.builder().setAps(aps).build()

                val androidConfig = AndroidConfig.builder()
                    .setNotification(
                        AndroidNotification.builder()
                            .setBodyLocalizationKey(key)
                            .addBodyLocalizationArg(user.name)
                            .build(),
                    )
                    .build()

                val message = MulticastMessage.builder()
                    .putData("notification_type", "NEW_SUBSCRIBE_REQUEST")
                    .setNotification(
                        FirebaseNotification.builder().setTitle(user.name).setImageScaled(user.image?.id).build(),
                    )
                    .setAndroidConfig(androidConfig)
                    .setApnsConfig(apnsConfig)

                sendMulticastMessage(firebaseMessaging, message, tokens, command.creatorId, log)
            }

            request
        }

        return request
    }

    fun execute(command: AcceptSubscribeRequest) {
        val request = subscribeRequestRepository.getById(command.requestId)
        log.info("User ${command.userId} accepting subscribe request from user ${request.userId}")

        if (request.creatorId != command.userId) {
            throw ForbiddenException()
        }

        val subscription = context.selectFrom(SUBSCRIPTION)
            .where(SUBSCRIPTION.USER_ID.eq(command.userId).and(SUBSCRIPTION.CREATOR_ID.eq(request.creatorId)))
            .and(JooqSubscriptionHelper.activeSubscription)
            .fetch()
        if (subscription.isNotEmpty) {
            throw ConflictException("User ${command.userId} already subscribes to creator ${request.creatorId}")
        }

        if (request.deletedAt != null || request.acceptedAt != null || request.declinedAt != null) {
            throw NotFoundException()
        }

        val creator = usersCollection[request.creatorId].get()
        val price = subscriberStripeRepository.priceFactory(creator, FREE_SUBSCRIBE_TIER)
        val customerId = subscriberStripeRepository.customerFactory(request.userId, FREE_SUBSCRIBE_TIER.currency)

        stripeSubscriptionService.createSubscription(
            customerId = customerId,
            paymentMethodId = null,
            couponId = null,
            tier = FREE_SUBSCRIBE_TIER,
            priceId = price.id,
            creatorId = request.creatorId,
            userId = request.userId,
            creatorStripeAccountId = null,
            onBehalfOf = null,
            creatorCountry = CZ_VAT_COUNTRY,
            creatorVatId = null,
            subscribed = Instant.now(),
            isResubscription = false,
            cardCreateType = null,
            currency = Currency.EUR,
        )

        context
            .update(Tables.SUBSCRIBE_REQUEST)
            .set(Tables.SUBSCRIBE_REQUEST.ACCEPTED_AT, Instant.now(clock))
            .where(Tables.SUBSCRIBE_REQUEST.ID.eq(command.requestId))
            .execute()

        val notification = Notification(
            userId = request.userId,
            type = NotificationType.SUBSCRIBE_REQUEST_ACCEPTED,
            actorIds = mutableListOf(request.creatorId),
            objectType = StorageEntityType.USER,
            objectId = request.creatorId,
            created = Instant.now(clock),
            timestamp = Instant.now(clock),
        )
        notificationRepository.save(notification)
        usersCollection[request.creatorId].field(pendingRequestsPath).increment(-1)
    }

    fun execute(command: DeclineSubscribeRequest) {
        val request = getValidRequest(command.requestId)

        if (request.creatorId != command.userId) {
            throw ForbiddenException()
        }

        context
            .update(Tables.SUBSCRIBE_REQUEST)
            .set(Tables.SUBSCRIBE_REQUEST.DECLINED_AT, Instant.now(clock))
            .where(Tables.SUBSCRIBE_REQUEST.ID.eq(command.requestId))
            .execute()

        usersCollection[request.creatorId].field(pendingRequestsPath).increment(-1)
    }

    fun execute(command: MarkSubscribeRequestsAsSeen) {
        context
            .update(Tables.SUBSCRIBE_REQUEST)
            .set(Tables.SUBSCRIBE_REQUEST.SEEN_AT, Instant.now(clock))
            .where(Tables.SUBSCRIBE_REQUEST.CREATOR_ID.eq(command.userId))
            .and(Tables.SUBSCRIBE_REQUEST.SEEN_AT.isNull)
            .execute()
    }

    fun execute(command: CancelSubscribeRequest) {
        val request = getValidRequest(command.requestId)

        if (request.userId != command.userId) {
            throw ForbiddenException()
        }

        context
            .update(Tables.SUBSCRIBE_REQUEST)
            .set(Tables.SUBSCRIBE_REQUEST.DELETED_AT, Instant.now(clock))
            .where(Tables.SUBSCRIBE_REQUEST.ID.eq(command.requestId))
            .execute()

        usersCollection[request.creatorId].field(pendingRequestsPath).increment(-1)
    }

    private fun getValidRequest(requestId: Long): SubscribeRequest {
        val request = subscribeRequestRepository.getById(requestId)

        if (request.deletedAt != null || request.acceptedAt != null || request.declinedAt != null) {
            throw NotFoundException()
        }

        return request
    }
}

data class RequestToSubscribe(val creatorId: String, val userId: String)

data class AcceptSubscribeRequest(val userId: String, val requestId: Long)

data class DeclineSubscribeRequest(val userId: String, val requestId: Long)

data class CancelSubscribeRequest(val userId: String, val requestId: Long)

data class MarkSubscribeRequestsAsSeen(val userId: String)

val FREE_SUBSCRIBE_TIER = Tier(FREE_SUBSCRIBER_TIER_ID, 0, false, Currency.EUR, false)
private val pendingRequestsPath = root(User::counts).path(SupportCounts::pendingRequests)
