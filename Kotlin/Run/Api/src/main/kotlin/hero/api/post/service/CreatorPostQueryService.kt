package hero.api.post.service

import com.fasterxml.jackson.annotation.JsonTypeInfo
import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.core.data.Sort
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.gcloud.MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Category
import hero.model.GjirafaLiveAsset
import hero.model.LiveVideoStatus
import hero.model.Post
import hero.model.SavedPost
import hero.model.Subscriber
import hero.model.topics.PostState
import hero.model.topics.PostState.PUBLISHED
import hero.repository.community.fetchMemberCommunityIds
import hero.repository.post.JooqPostHelper
import hero.repository.post.PostRepository
import hero.repository.post.PostType
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.cmp
import hero.sql.cmpBeforeCursor
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.POST_ASSET
import hero.sql.jooq.Tables.POST_VOTE
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.sql.jooq.Tables.USER
import hero.sql.jooq.Tables.WATCH_ACTIVITY
import hero.sql.jooq.getSingle
import hero.sql.orderBy
import hero.sql.orderByReversed
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.SelectConditionStep
import org.jooq.SelectJoinStep
import org.jooq.impl.DSL
import java.time.Instant
import java.util.UUID

class CreatorPostQueryService(
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
    private val categoriesCollection: TypedCollectionReference<Category>,
    private val savedPostsCollection: TypedCollectionReference<SavedPost>,
    private val postRepository: PostRepository,
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    fun execute(query: GetCreatorPostDetails): CreatorPostWithMeta {
        val fetchResult = context
            .select(JooqPostHelper.postFields)
            .select(POST_VOTE.VOTE_VALUE)
            .select(USER.HAS_POST_PREVIEWS)
            .from(POST)
            .leftJoin(POST_VOTE)
            .on(POST_VOTE.USER_ID.eq(query.userId).and(POST_VOTE.POST_ID.eq(POST.ID)))
            .join(USER).on(USER.ID.eq(POST.USER_ID))
            .where(POST.ID.eq(query.postId))
            .getSingle()

        val post = JooqPostHelper.mapRecordToEntity(fetchResult)
            .takeIf { it.messageThreadId == null }
            ?.takeIf { it.parentId == null }
            ?.takeIf { it.siblingId == null }
            ?.also { validatePostAccess(it, query.userId) }
            ?: throw NotFoundException("Post ${query.postId} was not found")

        val subscriptionInfo = query.userId
            ?.takeIf { query.userId != post.userId }
            ?.let { subscribersCollection.fetchActiveSubscription(it, post.userId) }

        val savedPostInfo = query.userId
            ?.let { userId ->
                savedPostsCollection[SavedPost.id(userId = userId, postId = query.postId)].fetch()
            }
            ?.let {
                SavedCreatorPostInfo(it.id, it.savedAt)
            }

        val communities = query.userId?.let { context.fetchMemberCommunityIds(it) } ?: emptySet()

        return CreatorPostWithMeta(
            post,
            fetchCategories(post.categories),
            subscriptionInfo,
            savedPostInfo,
            isPartOfCommunity = post.communityId
                ?.let { communityId -> UUID.fromString(communityId) }
                ?.let { communityId -> communities.contains(communityId) },
            voteValue = fetchResult[POST_VOTE.VOTE_VALUE] ?: 0,
            authorHasPostPreviews = fetchResult[USER.HAS_POST_PREVIEWS],
        )
    }

    fun execute(query: GetCreatorPosts): Page<CreatorPostWithMeta> {
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetCreatorPostsCursor>()
        val beforeCursor = query.pageable.beforeCursor?.fromBase64()?.fromJson<GetCreatorPostsCursor>()
        val cursor = afterCursor ?: beforeCursor
        val isAuthor = query.userId == query.creatorId
        val allowedPostStates = if (isAuthor) allowedPostSatesForOwner.toList() else listOf(PUBLISHED)

        val (postsWithVote, nextPageable, hasNext) = fetchPosts(query, cursor, allowedPostStates)
        val posts = postsWithVote.map { it.post }

        val categoryByIds = fetchCategories(posts.flatMap { it.categories }).associateBy { it.id }
        val savedPostByIds = query.userId?.let { userId ->
            posts
                .map { SavedPost.id(userId = userId, postId = it.id) }
                .chunked(MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR)
                .flatMap { savedPostsCollection.where(SavedPost::id).isIn(it).fetchAll() }
                .associateBy { it.id }
        } ?: emptyMap()

        // we don't need to fetch subscriptions if we are filtering by community since we are just going to check
        // if the user is part of the community of the post
        val subscriptionByCreatorId = if (query.filter?.communityId == null) {
            posts
                .map { it.userId }
                .distinct()
                .mapNotNull {
                    // TODO rewrite to something faster
                    if (query.userId != null) {
                        subscribersCollection.fetchActiveSubscription(query.userId, it)
                    } else {
                        null
                    }
                }
                .associateBy { it.creatorId }
        } else {
            emptyMap()
        }

        val communities = query.userId?.let { context.fetchMemberCommunityIds(it) } ?: emptySet()

        val mappedPosts = postsWithVote.map {
            val categories = it.post.categories.mapNotNull { categoryId -> categoryByIds[categoryId] }
            val savedPostInfo = query.userId
                ?.let { userId -> savedPostByIds[SavedPost.id(userId = userId, postId = it.post.id)] }
                ?.let { savedPost -> SavedCreatorPostInfo(savedPost.id, savedPost.savedAt) }
            CreatorPostWithMeta(
                it.post,
                categories,
                subscriptionByCreatorId[it.post.userId],
                savedPostInfo,
                isPartOfCommunity = it.post.communityId
                    ?.let { communityId -> UUID.fromString(communityId) }
                    ?.let { communityId -> communities.contains(communityId) },
                voteValue = it.voteValue,
                authorHasPostPreviews = it.authorHasPostPreviews,
            )
        }

        return Page(
            mappedPosts,
            nextPageable,
            hasNext,
        )
    }

    /**
     * Currently only creator can fetch the livestreams
     */
    fun execute(query: GetCreatorLivestreams): Page<Post> {
        val posts = postRepository.find {
            this
                .join(POST_ASSET).on(POST_ASSET.POST_ID.eq(POST.ID))
                .where(POST.USER_ID.eq(query.creatorId))
                .and(POST.TYPE.eq(PostType.CONTENT_POST.name))
                .and(
                    DSL.jsonbGetAttributeAsText(
                        POST_ASSET.METADATA,
                        GjirafaLiveAsset::liveStatus.name,
                    ).eq(LiveVideoStatus.LIVE.name),
                )
                .and(POST.STATE.`in`(allowedPostSatesForOwner))
        }

        return Page(posts, PageRequest(), false)
    }

    private fun fetchPosts(
        query: GetCreatorPosts,
        cursor: GetCreatorPostsCursor?,
        allowedPostStates: List<PostState>,
    ): Triple<List<PostWithFetchMeta>, Pageable, Boolean> {
        val sort = query.pageable.sort
        val sortBy = sort.by?.let { CreatorPostsSortingFields.valueOf(it) } ?: CreatorPostsSortingFields.PINNED_AT
        val posts = sortBy.postsFetcher()(query, cursor, allowedPostStates)
        val hasNext = posts.size > query.pageable.pageSize
        val pageSizePosts = posts.take(query.pageable.pageSize)

        return Triple(
            pageSizePosts.map {
                PostWithFetchMeta(
                    JooqPostHelper.mapRecordToEntity(it),
                    if (it.field(POST_VOTE.VOTE_VALUE) != null) it[POST_VOTE.VOTE_VALUE] ?: 0 else 0,
                    if (it.field(USER.HAS_POST_PREVIEWS) != null) it[USER.HAS_POST_PREVIEWS] else false,
                )
            },
            nextPageable(pageSizePosts, query.pageable, sortBy),
            hasNext,
        )
    }

    private fun fetchPostsOrderedByPinnedAt(
        query: GetCreatorPosts,
        cursor: GetCreatorPostsCursor?,
        allowedPostStates: List<PostState>,
    ): List<Record> {
        val sort = query.pageable.sort
        val posts =
            context
                .select(JooqPostHelper.postFields)
                .select(POST_VOTE.VOTE_VALUE)
                .select(USER.HAS_POST_PREVIEWS)
                .from(POST)
                .leftJoin(POST_VOTE)
                .on(POST_VOTE.USER_ID.eq(query.userId).and(POST_VOTE.POST_ID.eq(POST.ID)))
                .join(USER).on(USER.ID.eq(POST.USER_ID))
                .fetchPostsBasicConditions(query, allowedPostStates)
                .let {
                    when (cursor) {
                        null -> it.orderBy(POST.PINNED_AT.orderBy(sort), POST.PUBLISHED_AT.orderBy(sort))

                        is GetCreatorPostsPinnedAtCursor ->
                            it
                                .and(POST.PINNED_AT.cmp(cursor.lastPinnedAt, sort).or(POST.PINNED_AT.isNull))
                                .orderBy(POST.PINNED_AT.orderBy(sort), POST.PUBLISHED_AT.orderBy(sort))

                        is GetCreatorPostsPublishedAtCursor ->
                            it
                                .and(POST.PINNED_AT.isNull)
                                .and(POST.PUBLISHED_AT.cmp(cursor.lastPublishedAt, sort))
                                .orderBy(POST.PUBLISHED_AT.orderBy(sort))

                        else -> throw BadRequestException("Invalid cursor")
                    }
                }
                .limit(query.pageable.pageSize + 1)
                .fetch()

        return posts
    }

    private fun fetchPostsOrderedByWatchedAt(
        query: GetCreatorPosts,
        cursor: GetCreatorPostsCursor?,
        allowedPostStates: List<PostState>,
    ): List<Record> {
        val sort = query.pageable.sort
        val rowNumberField = DSL.rowNumber().over().partitionBy(WATCH_ACTIVITY.POST_ID)
            .orderBy(WATCH_ACTIVITY.WATCHED_AT.desc()).`as`("row_number")
        val cte = DSL.name("recent_watch_activity")
            .`as`(
                DSL
                    .select(
                        WATCH_ACTIVITY.POST_ID,
                        WATCH_ACTIVITY.WATCHED_AT,
                        rowNumberField,
                    )
                    .from(WATCH_ACTIVITY)
                    .where(WATCH_ACTIVITY.FINISHED.eq(false))
                    .and(WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE.eq(true))
                    .let {
                        if (query.userId != null) {
                            it.and(WATCH_ACTIVITY.USER_ID.eq(query.userId))
                        } else {
                            it.and(DSL.falseCondition())
                        }
                    }
                    .and(WATCH_ACTIVITY.DELETED_AT.isNull),
            )

        val posts = context
            .with(cte)
            .select(JooqPostHelper.postFields)
            .select(cte.field(WATCH_ACTIVITY.WATCHED_AT))
            .from(POST)
            .leftJoin(cte).on(POST.ID.eq(cte.field(WATCH_ACTIVITY.POST_ID)).and(cte.field(rowNumberField)?.eq(1)))
            .fetchPostsBasicConditions(query, allowedPostStates)
            .let {
                val watchedAt = cte.field(WATCH_ACTIVITY.WATCHED_AT)
                when (cursor) {
                    null -> it.orderBy(watchedAt?.orderBy(sort), POST.PUBLISHED_AT.orderBy(sort))

                    is GetCreatorPostsWatchedAtCursor ->
                        it
                            .and(watchedAt?.cmp(cursor.watchedAt, sort)?.or(watchedAt.isNull))
                            .orderBy(watchedAt?.orderBy(sort), POST.PUBLISHED_AT.orderBy(sort))

                    is GetCreatorPostsPublishedAtCursor ->
                        it
                            .and(watchedAt?.isNull)
                            .and(POST.PUBLISHED_AT.cmp(cursor.lastPublishedAt, sort))
                            .orderBy(POST.PUBLISHED_AT.orderBy(sort))

                    else -> error("Invalid cursor")
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()

        return posts
    }

    private fun fetchPostsOrderedByPublishedAt(
        query: GetCreatorPosts,
        @Suppress("Unused")
        cursor: GetCreatorPostsCursor?,
        allowedPostStates: List<PostState>,
    ): List<Record> {
        val sort = query.pageable.sort
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetCreatorPostsPublishedAtCursor>()
        val beforeCursor = query.pageable.beforeCursor?.fromBase64()?.fromJson<GetCreatorPostsPublishedAtCursor>()

        val posts = context
            .select(JooqPostHelper.postFields)
            .select(POST_VOTE.VOTE_VALUE)
            .select(USER.HAS_POST_PREVIEWS)
            .from(POST)
            .leftJoin(POST_VOTE)
            .on(POST_VOTE.USER_ID.eq(query.userId).and(POST_VOTE.POST_ID.eq(POST.ID)))
            .join(USER).on(USER.ID.eq(POST.USER_ID))
            .fetchPostsBasicConditions(query, allowedPostStates)
            .let {
                when {
                    beforeCursor == null && afterCursor == null -> {
                        it.orderBy(POST.PUBLISHED_AT.orderBy(sort))
                    }

                    afterCursor != null -> {
                        it
                            .and(POST.PUBLISHED_AT.cmp(afterCursor.lastPublishedAt, sort))
                            .orderBy(POST.PUBLISHED_AT.orderBy(sort))
                    }

                    beforeCursor != null -> {
                        it
                            .and(POST.PUBLISHED_AT.cmpBeforeCursor(beforeCursor.lastPublishedAt, sort))
                            .orderBy(POST.PUBLISHED_AT.orderByReversed(sort))
                    }

                    else -> {
                        throw BadRequestException("Invalid cursor value")
                    }
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()

        // just reverse in place, so we don't create new array
        if (afterCursor == null && beforeCursor != null) {
            posts.reverse()
        }

        return posts
    }

    private fun fetchPostsOrderedByQuerySimilarity(
        query: GetCreatorPosts,
        cursor: GetCreatorPostsCursor?,
        allowedPostStates: List<PostState>,
    ): List<Record> {
        val sort = query.pageable.sort
        if (query.filter?.query?.isNotBlank() != true) {
            throw BadRequestException("Cannot sort by similarity if query is empty")
        }

        val similarityField = similarityField(query.filter.query)
        val similarityFieldNamed = similarityField.`as`("similarity")
        val posts = context
            .select(JooqPostHelper.postFields)
            .select(similarityFieldNamed)
            .from(POST)
            .fetchPostsBasicConditions(query, allowedPostStates)
            .let {
                when (cursor) {
                    null -> it.orderBy(similarityFieldNamed.orderBy(sort))

                    is GetCreatorPostsSimilarityCursor ->
                        it
                            .and(similarityField.cmp(cursor.similarity, sort))
                            .orderBy(similarityFieldNamed.orderBy(sort))

                    else -> {
                        throw BadRequestException("Invalid cursor value")
                    }
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()

        return posts
    }

    private fun fetchPostsOrderedByViews(
        query: GetCreatorPosts,
        cursor: GetCreatorPostsCursor?,
        allowedPostStates: List<PostState>,
    ): List<Record> {
        val sort = query.pageable.sort

        val posts = context
            .select(JooqPostHelper.postFields)
            .from(POST)
            .fetchPostsBasicConditions(query, allowedPostStates)
            .let {
                when (cursor) {
                    null -> it.orderBy(POST.VIEWS.orderBy(sort), POST.PUBLISHED_AT.orderBy(sort))

                    is GetCreatorPostsViewsCursor ->
                        it
                            .and(
                                if (sort.direction == Sort.Direction.ASC) {
                                    DSL.row(POST.VIEWS, POST.PUBLISHED_AT).gt(
                                        DSL.row(
                                            cursor.viewsCount,
                                            cursor.publishedAt,
                                        ),
                                    )
                                } else {
                                    DSL.row(POST.VIEWS, POST.PUBLISHED_AT).lt(
                                        DSL.row(
                                            cursor.viewsCount,
                                            cursor.publishedAt,
                                        ),
                                    )
                                },
                            )
                            .orderBy(POST.VIEWS.orderBy(sort), POST.PUBLISHED_AT.orderBy(sort))

                    else -> error("Invalid cursor")
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()

        return posts
    }

    private fun similarityField(query: String) =
        DSL.field(
            "word_similarity(immutable_unaccent({0}), immutable_unaccent({1}))",
            Double::class.java,
            query,
            POST.TEXT,
        )

    private fun SelectJoinStep<out Record>.fetchPostsBasicConditions(
        query: GetCreatorPosts,
        allowedPostStates: List<PostState>,
    ): SelectConditionStep<out Record> =
        this
            .let {
                if (query.creatorId != null) {
                    it.where(POST.USER_ID.eq(query.creatorId))
                } else if (query.filter?.type == PostFilterType.IN_PROGRESS && query.userId != null) {
                    // if client requests posts from all subscribed creators and also wants only posts in progress
                    // we will just use the column subscription_active from watch_activity table to skip this subquery
                    it.where(DSL.trueCondition())
                } else if (query.filter?.communityId != null) {
                    it.where(DSL.trueCondition())
                } else if (query.userId != null) {
                    val creatorIds = context.select(SUBSCRIPTION.CREATOR_ID).from(SUBSCRIPTION)
                        .where(SUBSCRIPTION.USER_ID.eq(query.userId))
                        .and(JooqSubscriptionHelper.activeSubscription)
                        .fetch()
                        .map { record -> record.value1() }
                    it.where(POST.USER_ID.`in`(creatorIds + listOfNotNull(query.userId)))
                } else {
                    it.where(DSL.falseCondition())
                }
            }
            .let {
                val communityId = query.filter?.communityId
                if (communityId != null) {
                    it.and(POST.COMMUNITY_ID.eq(communityId))
                } else {
                    // threads that creator posted in communities must not be shown on his profile
                    it.and(POST.COMMUNITY_ID.isNull)
                }
            }
            .and(POST.TYPE.eq(PostType.CONTENT_POST.name))
            .and(POST.STATE.`in`(allowedPostStates))
            .let {
                if (query.filter?.excludedCreatorIds?.isNotEmpty() == true) {
                    it.and(POST.USER_ID.notIn(query.filter.excludedCreatorIds))
                } else {
                    it
                }
            }
            .let {
                val categoryId = query.filter?.categoryId
                if (categoryId != null) it.and(POST.CATEGORIES.contains(arrayOf(categoryId))) else it
            }
            .let {
                if (query.filter?.query?.isNotBlank() == true) {
                    it.and(
                        DSL.condition(
                            "immutable_unaccent({0}) <% immutable_unaccent({1})",
                            query.filter.query,
                            POST.TEXT,
                        ),
                    )
                } else {
                    it
                }
            }
            .let {
                if (query.filter?.type == PostFilterType.IN_PROGRESS && query.userId != null) {
                    it.and(
                        POST.ID.`in`(
                            DSL.select(WATCH_ACTIVITY.POST_ID).from(WATCH_ACTIVITY)
                                .where(WATCH_ACTIVITY.USER_ID.eq(query.userId))
                                .and(WATCH_ACTIVITY.FINISHED.isFalse)
                                .and(WATCH_ACTIVITY.DELETED_AT.isNull)
                                .and(WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE.isTrue),
                        ),
                    )
                } else {
                    it
                }
            }

    private fun nextPageable(
        posts: List<Record>,
        pageable: Pageable,
        sortBy: CreatorPostsSortingFields,
    ): Pageable {
        fun Record.toCursor() =
            if (sortBy == CreatorPostsSortingFields.PINNED_AT && this[POST.PINNED_AT] != null) {
                GetCreatorPostsPinnedAtCursor(this[POST.PINNED_AT])
            } else if (sortBy == CreatorPostsSortingFields.WATCHED_AT && this[WATCH_ACTIVITY.WATCHED_AT] != null) {
                GetCreatorPostsWatchedAtCursor(this[WATCH_ACTIVITY.WATCHED_AT])
            } else if (sortBy == CreatorPostsSortingFields.VIEWS) {
                GetCreatorPostsViewsCursor(this[POST.VIEWS], this[POST.PUBLISHED_AT])
            } else if (sortBy == CreatorPostsSortingFields.QUERY_SIMILARITY) {
                val similarity = this[DSL.field("similarity", Double::class.java)]
                GetCreatorPostsSimilarityCursor(similarity)
            } else {
                GetCreatorPostsPublishedAtCursor(this[POST.PUBLISHED_AT])
            }

        val afterCursor = posts
            .lastOrNull()
            ?.toCursor()
            ?.toJson()?.toBase64()

        val beforeCursor = posts
            .firstOrNull()
            ?.toCursor()
            ?.toJson()?.toBase64()

        return PageRequest(
            -1,
            pageable.pageSize,
            beforeCursor = beforeCursor,
            afterCursor = afterCursor,
            sort = pageable.sort,
        )
    }

    private fun fetchCategories(categories: List<String>): List<Category> =
        categories.chunked(MAX_ARRAY_SIZE_IN_ARRAY_OPERATOR).flatMap {
            categoriesCollection.where(Category::id).isIn(it).fetchAll()
        }

    private fun CreatorPostsSortingFields.postsFetcher(): (
        GetCreatorPosts,
        GetCreatorPostsCursor?,
        List<PostState>,
    ) -> List<Record> =
        when (this) {
            CreatorPostsSortingFields.PINNED_AT -> ::fetchPostsOrderedByPinnedAt

            CreatorPostsSortingFields.PUBLISHED_AT -> ::fetchPostsOrderedByPublishedAt

            CreatorPostsSortingFields.WATCHED_AT -> ::fetchPostsOrderedByWatchedAt

            CreatorPostsSortingFields.VIEWS -> ::fetchPostsOrderedByViews

            CreatorPostsSortingFields.QUERY_SIMILARITY -> ::fetchPostsOrderedByQuerySimilarity
        }
}

data class GetCreatorPosts(
    val userId: String?,
    val creatorId: String?,
    val pageable: Pageable,
    val filter: GetCreatorPostsFilter? = null,
)

data class GetCreatorPostsFilter(
    val type: PostFilterType? = null,
    val categoryId: String? = null,
    val communityId: UUID? = null,
    val query: String? = null,
    val excludedCreatorIds: List<String> = emptyList(),
)

enum class PostFilterType {
    IN_PROGRESS,
}

data class GetCreatorPostDetails(val userId: String?, val postId: String)

data class GetCreatorLivestreams(val creatorId: String)

data class CreatorPostWithMeta(
    val post: Post,
    val categories: List<Category>,
    val subscriptionInfo: Subscriber?,
    val savedPostInfo: SavedCreatorPostInfo? = null,
    val isPartOfCommunity: Boolean? = null,
    // currently authenticated user's vote value
    val voteValue: Int = 0,
    val authorHasPostPreviews: Boolean = false,
)

data class SavedCreatorPostInfo(val id: String, val savedAt: Instant)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME)
private sealed class GetCreatorPostsCursor

private data class GetCreatorPostsPinnedAtCursor(val lastPinnedAt: Instant) : GetCreatorPostsCursor()

private data class GetCreatorPostsPublishedAtCursor(val lastPublishedAt: Instant) : GetCreatorPostsCursor()

private data class GetCreatorPostsWatchedAtCursor(val watchedAt: Instant) : GetCreatorPostsCursor()

private data class GetCreatorPostsViewsCursor(val viewsCount: Long, val publishedAt: Instant) : GetCreatorPostsCursor()

private data class GetCreatorPostsSimilarityCursor(val similarity: Double) : GetCreatorPostsCursor()

enum class CreatorPostsSortingFields {
    PINNED_AT,
    PUBLISHED_AT,
    WATCHED_AT,
    VIEWS,
    QUERY_SIMILARITY,
}

private data class PostWithFetchMeta(val post: Post, val voteValue: Int, val authorHasPostPreviews: Boolean)
