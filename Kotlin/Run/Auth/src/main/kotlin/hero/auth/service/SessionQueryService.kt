package hero.auth.service

import hero.exceptions.http.ForbiddenException
import hero.http4k.auth.REFRESH_TOKEN_VALIDITY_SECONDS
import hero.model.Session
import hero.repository.session.JooqSessionHelper
import hero.repository.session.SessionRepository
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.UUID

class SessionQueryService(
    private val sessionRepository: SessionRepository,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(query: GetActiveSessions): List<Session> =
        context
            .select(JooqSessionHelper.sessionFields)
            .from(Tables.SESSION)
            .where(Tables.SESSION.USER_ID.eq(query.userId))
            .and(Tables.SESSION.REFRESHED_AT.gt(Instant.now(clock).minusSeconds(REFRESH_TOKEN_VALIDITY_SECONDS)))
            .and(Tables.SESSION.REVOKED.isFalse)
            .orderBy(Tables.SESSION.REFRESHED_AT.desc())
            .map { JooqSessionHelper.mapRecordToEntity(it) }

    fun execute(query: FindSession): Session? {
        val session = sessionRepository.findById(UUID.fromString(query.sessionId)) ?: return null

        if (session.userId != query.userId) {
            throw ForbiddenException()
        }

        return session
    }
}

data class GetActiveSessions(val userId: String)

data class FindSession(val sessionId: String, val userId: String)
