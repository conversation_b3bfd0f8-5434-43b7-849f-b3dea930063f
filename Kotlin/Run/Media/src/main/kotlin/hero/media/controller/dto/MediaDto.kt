package hero.media.controller.dto

import hero.model.StorageEntityType

data class MediaU<PERSON>loadBody(
    // necessary to provide correct content-type to avoid CORS errors:
    // https://stackoverflow.com/q/62353634/922584
    // https://github.com/googleapis/java-storage/issues/619
    val contentType: String,
    val targetEntityType: StorageEntityType,
    val nonce: String,
)

data class StorageUploadSignedUrl(
    val id: String,
    val uploadUrl: String,
    val previewUrl: String?,
)
