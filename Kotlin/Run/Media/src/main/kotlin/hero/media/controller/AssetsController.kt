package hero.media.controller

import hero.baseutils.log
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.imageProxy
import hero.http4k.auth.parseJwtUser
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.media.controller.dto.MediaUploadBody
import hero.media.controller.dto.StorageUploadSignedUrl
import hero.media.controller.dto.mediaUploadBodyExample
import hero.media.controller.dto.storageUploadFromUrlRequestExample
import hero.media.controller.dto.storageUploadResponseExample
import hero.media.controller.dto.storageUploadSignedUrlExample
import hero.media.service.StorageUploadsService
import hero.media.service.StorageUploadsService.AssetType.IMAGE
import hero.media.service.mediaType
import hero.model.StorageUploadFromUrlPostBody
import hero.model.StorageUploadResponse
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header

class AssetsController(
    storageUploadsService: StorageUploadsService,
) {
    @Suppress("unused")
    val routeAssetUploadUrl: ContractRoute =
        "/v1/uploads".post(
            summary = "Gets a URL for a new upload.",
            tag = "Images",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example storageUploadSignedUrlExample),
            receiving = mediaUploadBodyExample,
            handler = { request, _ ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val body = lens<MediaUploadBody>(request)
                val mediaType = body.contentType.mediaType
                val uploadInfo = storageUploadsService.objectName(
                    body.targetEntityType.name,
                    jwtUser.id,
                    body.contentType,
                    body.nonce,
                )
                log.info(
                    "User prepares to upload a new ${body.targetEntityType.name.lowercase()} $mediaType.",
                    mapOf("userId" to jwtUser.id),
                )
                Response(Status.OK)
                    .body(
                        StorageUploadSignedUrl(
                            id = uploadInfo.publicUrl,
                            uploadUrl = storageUploadsService.uploadUrl(uploadInfo.objectName),
                            previewUrl = if (mediaType == IMAGE) uploadInfo.publicUrl.imageProxy() else null,
                        ),
                    )
            },
        )

    @Suppress("unused")
    val routeAssetFromUrl: ContractRoute =
        "/v1/uploads-from-url".post(
            summary = "Uploads image from a given URL.",
            tag = "Images",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example storageUploadResponseExample),
            receiving = storageUploadFromUrlRequestExample,
            handler = { request, _ ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val body = lens<StorageUploadFromUrlPostBody>(request)
                log.info(
                    "User stores ${body.targetEntityType.name.lowercase()} image from given URL.",
                    mapOf("userId" to jwtUser.id, "sourceUrl" to body.sourceUrl),
                )
                val targetUrl =
                    storageUploadsService.uploadFromUrl(body.targetEntityType.name, jwtUser.id, body.sourceUrl)
                Response(Status.OK)
                    .body(StorageUploadResponse(targetUrl))
            },
        )
}
