package hero.contract.api

import com.github.kittinunf.fuel.httpDelete
import com.github.kittinunf.fuel.httpGet
import hero.baseutils.fetch
import hero.baseutils.serviceCall
import hero.contract.api.dto.PagedPostResponse
import hero.jwt.ACCESS_TOKEN
import hero.jwt.authorization

fun fetchCreatorPosts(
    creatorId: String,
    userId: String? = null,
    afterCursor: String? = null,
    pageSize: Int = 20,
): PagedPostResponse =
    serviceCall("api", "/v1/users/$creatorId/posts")
        .httpGet(listOf("pageSize" to pageSize, "afterCursor" to afterCursor))
        .let {
            if (userId != null) {
                it.header("Cookie", "$ACCESS_TOKEN=${userId.authorization()}")
            } else {
                it
            }
        }
        .fetch<PagedPostResponse>()

fun disableDevice(
    userId: String,
    deviceId: String,
) {
    serviceCall("api", "/v1/devices/$deviceId")
        .httpDelete()
        .header("Cookie", "$ACCESS_TOKEN=${userId.authorization()}")
        .response()
}
