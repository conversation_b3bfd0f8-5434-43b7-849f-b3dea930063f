package hero.core.detekt

import io.gitlab.arturbosch.detekt.api.CodeSmell
import io.gitlab.arturbosch.detekt.api.Config
import io.gitlab.arturbosch.detekt.api.Debt
import io.gitlab.arturbosch.detekt.api.Entity
import io.gitlab.arturbosch.detekt.api.Issue
import io.gitlab.arturbosch.detekt.api.Rule
import io.gitlab.arturbosch.detekt.api.RuleSet
import io.gitlab.arturbosch.detekt.api.RuleSetProvider
import io.gitlab.arturbosch.detekt.api.Severity
import org.jetbrains.kotlin.psi.KtStringTemplateExpression

/**
 * A Detekt rule that enforces the use of curly braces in string interpolations
 * when accessing properties or methods, e.g., `${variable.property}`. This rule prevents
 * accidental leakage of data classes via string interpolation, which we had in e454ea4b9df5, where we added
 * hyperlink `"$hostname/$user.id"`, so the entire user was serialized, instead of just adding the id into the path.
 *
 * Purpose:
 * - Ensures clarity by explicitly delimiting interpolated expressions.
 * - Prevents potential ambiguities or misinterpretation in string templates.
 *
 * Non-Compliant Example:
 * ```
 * val message = "Value is $variable.property"
 * ```
 *
 * Compliant Example:
 * ```
 * val message = "Value is ${variable.property}"
 * ```
 */
class CurlyBraceInStringInterpolationRule(config: Config) : Rule(config) {
    override val issue = Issue(
        javaClass.simpleName,
        Severity.Security,
        "String interpolations with dot following must have curly brace.",
        Debt.FIVE_MINS,
    )
    private val regex = Regex("""\$\w+\.\w+""")

    override fun visitStringTemplateExpression(expression: KtStringTemplateExpression) {
        super.visitStringTemplateExpression(expression)

        if (regex.find(expression.text) != null) {
            report(
                CodeSmell(
                    issue,
                    Entity.from(expression),
                    message = "Wrong string interpolation ${expression.text}",
                ),
            )
        }
    }
}

@Suppress("Unused")
class CustomRuleSetProvider : RuleSetProvider {
    override val ruleSetId: String = "custom-rules"

    override fun instance(config: Config): RuleSet =
        RuleSet(
            ruleSetId,
            listOf(
                CurlyBraceInStringInterpolationRule(config),
            ),
        )
}
