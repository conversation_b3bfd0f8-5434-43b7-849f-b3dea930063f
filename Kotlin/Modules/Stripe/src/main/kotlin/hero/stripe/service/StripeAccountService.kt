package hero.stripe.service

import com.stripe.exception.InvalidRequestException
import com.stripe.model.Account
import com.stripe.model.AccountLink
import com.stripe.param.AccountCreateParams.BusinessProfile
import com.stripe.param.AccountCreateParams.BusinessType
import com.stripe.param.AccountCreateParams.Capabilities
import com.stripe.param.AccountCreateParams.Company
import com.stripe.param.AccountCreateParams.Individual
import com.stripe.param.AccountCreateParams.Individual.Dob
import com.stripe.param.AccountCreateParams.Settings
import com.stripe.param.AccountCreateParams.builder
import com.stripe.param.AccountLinkCreateParams
import com.stripe.param.AccountUpdateParams
import com.stripe.param.PersonCollectionCreateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.model.CompanyType
import hero.model.Currency
import hero.model.Tier
import hero.model.User
import hero.model.euCountries
import java.time.LocalDate
import com.stripe.param.AccountCreateParams.Type as AccountType

class StripeAccountService(
    private val clients: StripeClients,
    private val hostname: String,
    private val hostnameServices: String,
) {
    // then we try to lower them to shortened value if possible
    private val expressPayoutUpdateParams = AccountUpdateParams.builder()
        .setSettings(
            AccountUpdateParams.Settings.builder().setPayouts(
                AccountUpdateParams.Settings.Payouts.builder().setSchedule(
                    AccountUpdateParams.Settings.Payouts.Schedule.builder()
                        .setDelayDays(DELAY_DAYS_SHORTENED)
                        .build(),
                ).build(),
            ).build(),
        ).build()

    // WARN: The account (with its type) is created on first (!) connect button click,
    // if you change the account type, it will not change already created accounts and
    // clicking the connect button will continue to be with the previous account type.
    fun createAccount(
        user: User,
        // TODO remove when HH-2305 is done
        forceClientCurrency: Currency? = null,
    ): Account {
        val tier = Tier.ofId(user.creator.tierId)
        val company = user.company
            ?: throw BadRequestException(
                "Cannot create a Stripe account for ${user.id} without created company object.",
                mapOf("userId" to user.id),
            )
        if (company.country?.nullIfEmpty() == null) {
            throw BadRequestException(
                "Cannot create a Stripe account for ${user.id} without country filled in.",
                mapOf("userId" to user.id),
            )
        }
        if (company.companyType != CompanyType.NO_COMPANY) {
            if (company.companyType == CompanyType.LEGAL_ENTITY && company.name.isNullOrBlank()) {
                throw BadRequestException(
                    "Business must have its name filled in for ${user.id}.",
                    mapOf("userId" to user.id),
                )
            }
            if (company.id.nullIfEmpty() == null) {
                throw BadRequestException(
                    "Business must have its id filled in for ${user.id}.",
                    mapOf("userId" to user.id),
                )
            }
        }

        if (company.firstName.isNullOrBlank() && company.lastName.isNullOrBlank()) {
            throw BadRequestException(
                "Responsible person's name for ${user.id} must be filled in.",
                mapOf("userId" to user.id),
            )
        }

        if (company.birthDate.isNullOrBlank()) {
            throw BadRequestException("Birthdate of responsible person for ${user.id} cannot be empty.")
        }

        val birthDate = try {
            LocalDate.parse(company.birthDate)
        } catch (e: Exception) {
            throw BadRequestException("Birthdate ${company.birthDate} cannot be parsed for ${user.id}.")
        }

        // for some reason Stripe sometimes does not accept completely valid phones, see:
        // https://dashboard.stripe.com/support/sco_QFXTZkEyTvT8t9
        val cleanPhone = company.phone?.replace("[()\\s-]".toRegex(), "")

        val accountParams = builder()
            // we must set Country manually first to set cardCapabilities
            // https://linear.app/herohero/issue/HH-560/unable-to-change-the-country-in-stripe-when-connecting-new-account
            // https://linear.app/herohero/issue/HH-446/ux-flow-for-selecting-country-before-selecting-tier
            .setCountry(company.country)
            .setEmail(user.email)
            // Note that Stripe does not distinguish physical persons with or without business ids,
            // only real companies with board and owners are considered as companies. Company ids for individuals
            // are "ignored" by Stripe and only birthdate is required. This corresponds to the Stripe's own
            // onboarding tool, which is more informative than the documentation, where this is unclear.
            .setBusinessType(
                if (company.companyType == CompanyType.LEGAL_ENTITY) BusinessType.COMPANY else BusinessType.INDIVIDUAL,
            )
            .setBusinessProfile(
                BusinessProfile.builder()
                    .setName(company.name)
                    .setSupportEmail(user.email)
                    .setSupportPhone(cleanPhone)
                    // we intentionally keep the `id` and not `path` for the user to be found
                    // any time after path changes or deletions
                    .setUrl("$hostname/${user.id}")
                    // 5815: Digital Goods Media
                    // https://docs.stripe.com/connect/setting-mcc#list
                    .setMcc("5815")
                    .build(),
            )
            .setCapabilities(cardCapabilities)
            .setType(AccountType.EXPRESS)
            .setDefaultCurrency(tier.currency.name)
            .setSettings(
                Settings.builder()
                    .setPayments(
                        Settings.Payments.builder()
                            .setStatementDescriptor(bankStatementDescriptor(user.name))
                            .build(),
                    )
                    .setPayouts(
                        Settings.Payouts.builder()
                            .setSchedule(expressPayoutCreateParams)
                            .build(),
                    ).build(),
            )
            .also {
                if (company.companyType != CompanyType.LEGAL_ENTITY) {
                    it.setIndividual(
                        Individual.builder()
                            .setFirstName(company.firstName)
                            .setLastName(company.lastName)
                            .setEmail(user.email)
                            .setPhone(cleanPhone)
                            .setAddress(
                                Individual.Address.builder()
                                    .setLine1(company.address)
                                    .setCountry(company.country!!)
                                    .setPostalCode(company.postalCode)
                                    .setCity(company.city)
                                    .setState(company.state.nullIfEmpty())
                                    .build(),
                            )
                            .setDob(
                                Dob.builder()
                                    .setDay(birthDate.dayOfMonth.toLong())
                                    .setMonth(birthDate.monthValue.toLong())
                                    .setYear(birthDate.year.toLong())
                                    .build(),
                            )
                            .build(),
                    )
                } else {
                    it.setCompany(
                        Company.builder()
                            .setName(company.name)
                            .setTaxId(company.id)
                            .setRegistrationNumber(company.id)
                            .setVatId(company.vatId)
                            .setPhone(cleanPhone)
                            .setAddress(
                                Company.Address.builder()
                                    .setLine1(company.address)
                                    .setCountry(company.country!!)
                                    .setPostalCode(company.postalCode)
                                    .setCity(company.city)
                                    .setState(company.state.nullIfEmpty())
                                    .build(),
                            )
                            .build(),
                    )
                }
            }
            .putAllExtraParam(
                // TODO allow in prod for all countries
                if (SystemEnv.environment == "prod" && company.country?.uppercase() in euCountries)
                    emptyMap<String, Any?>()
                else
                    mapOf(
                        "additional_verifications[document][apply_to][0]" to "representative",
                        "additional_verifications[document][upfront][0][disables]" to "payouts_and_payments",
                        "additional_verifications[document][require_matching_selfie]" to true,
                        "additional_verifications[document][requested]" to true,
                    ),
            )
            .build()

        // TODO remove forceClientCurrency when HH-2305 is done
        val client = clients[forceClientCurrency ?: tier.currency]

        val account = retry {
            try {
                client.accounts().create(accountParams)
            } catch (e: InvalidRequestException) {
                throw BadRequestException(e.message, mapOf("userId" to user.id))
            }
        }

        if (company.companyType == CompanyType.LEGAL_ENTITY) {
            account.persons().create(
                PersonCollectionCreateParams.builder()
                    .setRelationship(
                        PersonCollectionCreateParams.Relationship.builder()
                            .setOwner(true).setRepresentative(true).build(),
                    )
                    .setFirstName(company.firstName)
                    .setLastName(company.lastName)
                    .setEmail(user.email)
                    .setPhone(cleanPhone)
                    .setAddress(
                        PersonCollectionCreateParams.Address.builder()
                            .setLine1(company.address)
                            .setCountry(company.country!!)
                            .setPostalCode(company.postalCode)
                            .setCity(company.city)
                            .setState(company.state.nullIfEmpty())
                            .build(),
                    )
                    .setDob(
                        PersonCollectionCreateParams.Dob.builder()
                            .setDay(birthDate.dayOfMonth.toLong())
                            .setMonth(birthDate.monthValue.toLong())
                            .setYear(birthDate.year.toLong())
                            .build(),
                    )
                    .build(),
            )
        }

        tryLowerPayoutDelay(user.id, account.id, account.country, tier.currency)
        return account
    }

    fun updateAccount(user: User) {
        val stripeAccountId = user.creator.stripeAccountId
            ?: error("This method must not be called for creators (${user.id}) without stripeAccountId.")

        if (user.company == null) {
            return
        }

        val tier = Tier.ofId(user.creator.tierId)
        tryLowerPayoutDelay(user.id, stripeAccountId, user.company?.country ?: "CZ", tier.currency)

        // TODO https://linear.app/herohero/issue/HH-837/migration-from-express-to-custom-accounts
        val accountParams = AccountUpdateParams.builder()
            .setBusinessProfile(
                AccountUpdateParams.BusinessProfile.builder()
                    .setName(user.company!!.name)
                    .setSupportEmail(user.email)
                    .setUrl("$hostname/${user.path}")
                    .build(),
            )
            .setSettings(
                AccountUpdateParams.Settings.builder()
                    .setPayments(
                        AccountUpdateParams.Settings.Payments.builder()
                            .setStatementDescriptor(bankStatementDescriptor(user.name))
                            .build(),
                    )
                    .build(),
            )
            .build()

        val client = clients[tier.currency]
        retry {
            client.accounts().update(stripeAccountId, accountParams)
        }
    }

    // see above concerning changing account types
    fun createAccountLink(
        userId: String,
        accountId: String,
        token: String,
        currency: Currency,
    ): AccountLink {
        val linkParams = AccountLinkCreateParams.builder()
            .setAccount(accountId)
            .setRefreshUrl("$hostnameServices/v1/stripe/return?userId=$userId&token=$token")
            .setReturnUrl("$hostnameServices/v1/stripe/return?userId=$userId&token=$token")
            .setType(AccountLinkCreateParams.Type.ACCOUNT_ONBOARDING)
            .build()

        return retry {
            try {
                clients[currency].accountLinks().create(linkParams)
            } catch (e: InvalidRequestException) {
                if ("account has been rejected" in (e.message ?: "")) {
                    throw ForbiddenException(e.message, mapOf("userId" to userId))
                }
                throw e
            }
        }
    }

    /** see: https://linear.app/herohero/issue/HH-863/stripe-payout-delay-spec */
    private fun tryLowerPayoutDelay(
        userId: String,
        accountId: String,
        country: String,
        currency: Currency,
    ) {
        try {
            clients[currency].accounts().update(accountId, expressPayoutUpdateParams)
        } catch (e: Exception) {
            log.info(
                "Stripe account $accountId (from $country) cannot have payout delay lowered: ${e.message}",
                mapOf("userId" to userId),
            )
        }
    }
}

/** All connected accounts are allowed to have 7-day payout delay */
private const val DELAY_DAYS_DEFAULT = 7L
/** Only selected countries (EU+US) can be shortened for now. Additional countries must be requested via Stripe support. */
private const val DELAY_DAYS_SHORTENED = 5L

// we create all new Stripe account with DELAY_DAYS_DEFAULT payout delay, see comment on the constant
// see: https://linear.app/herohero/issue/HH-863/stripe-payout-delay-spec
val expressPayoutCreateParams = Settings.Payouts.Schedule.builder()
    .setInterval(Settings.Payouts.Schedule.Interval.MONTHLY)
    .setMonthlyAnchor(1)
    .setDelayDays(DELAY_DAYS_DEFAULT)
    .build()

val cardCapabilities = Capabilities.builder()
    .setCardPayments(Capabilities.CardPayments.builder().setRequested(true).build())
    .setTransfers(Capabilities.Transfers.builder().setRequested(true).build())
    .build()
