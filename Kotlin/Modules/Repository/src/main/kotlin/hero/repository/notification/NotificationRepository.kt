package hero.repository.notification

import hero.model.Notification
import hero.model.NotificationType
import hero.model.StorageEntityType
import hero.sql.jooq.Tables.NOTIFICATION
import hero.sql.jooq.getSingle
import hero.sql.jooq.tables.records.NotificationRecord
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.ResultQuery
import org.jooq.SelectWhereStep
import java.time.Instant
import java.util.UUID

class NotificationRepository(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    constructor(context: DSLContext) : this(lazy { context })

    fun save(notification: Notification): Notification {
        val notificationRecord = mapEntityToRecord(notification)

        context
            .insertInto(NOTIFICATION)
            .set(notificationRecord)
            .onDuplicateKeyUpdate()
            .set(notificationRecord)
            .execute()

        return notification
    }

    fun getById(notificationId: String): Notification =
        context
            .select(notificationFields)
            .from(NOTIFICATION)
            .where(NOTIFICATION.ID.eq(notificationId))
            .getSingle({ mapRecordToEntity(it) })

    fun saveAll(notifications: Collection<Notification>): Collection<Notification> {
        val notificationRecords = notifications.map { mapEntityToRecord(it) }
        val notificationIds = notifications.map { it.id }
        context.deleteFrom(NOTIFICATION)
            .where(NOTIFICATION.ID.`in`(notificationIds))
            .execute()

        context.insertInto(NOTIFICATION)
            .set(notificationRecords)
            .execute()

        return notifications
    }

    fun delete(
        notificationId: String,
        deletedAt: Instant = Instant.now(),
    ) {
        context
            .update(NOTIFICATION)
            .set(NOTIFICATION.DELETED_AT, deletedAt)
            .where(NOTIFICATION.ID.eq(notificationId))
            .execute()
    }

    fun find(condition: SelectWhereStep<out Record>.() -> ResultQuery<out Record>): List<Notification> =
        context
            .select(notificationFields)
            .from(NOTIFICATION)
            .condition()
            .fetch()
            .map { mapRecordToEntity(it) }

    private fun mapEntityToRecord(notification: Notification): NotificationRecord =
        NotificationRecord().apply {
            id = notification.id
            type = notification.type.name
            userId = notification.userId
            actorIds = notification.actorIds.toTypedArray()
            createdAt = notification.created
            seenAt = notification.seenAt
            checkedAt = notification.checkedAt
            when (notification.objectType) {
                StorageEntityType.POST -> objectPostId = notification.objectId
                StorageEntityType.USER -> objectUserId = notification.objectId
            }
            communityId = notification.communityId?.let { UUID.fromString(it) }
        }

    private fun mapRecordToEntity(record: Record): Notification {
        val objectPostId = record[NOTIFICATION.OBJECT_POST_ID]
        val objectUserId = record[NOTIFICATION.OBJECT_USER_ID]
        val storageEntityType = if (objectUserId != null) StorageEntityType.USER else StorageEntityType.POST
        return Notification(
            id = record[NOTIFICATION.ID],
            userId = record[NOTIFICATION.USER_ID],
            type = NotificationType.valueOf(record[NOTIFICATION.TYPE]),
            actorIds = record[NOTIFICATION.ACTOR_IDS].toList(),
            objectType = storageEntityType,
            objectId = objectUserId ?: objectPostId,
            created = record[NOTIFICATION.CREATED_AT],
            timestamp = record[NOTIFICATION.CREATED_AT],
            checkedAt = record[NOTIFICATION.CHECKED_AT],
            seenAt = record[NOTIFICATION.SEEN_AT],
            communityId = record[NOTIFICATION.COMMUNITY_ID]?.toString(),
        )
    }
}

private val notificationFields = listOf(
    NOTIFICATION.ID,
    NOTIFICATION.OBJECT_POST_ID,
    NOTIFICATION.OBJECT_USER_ID,
    NOTIFICATION.USER_ID,
    NOTIFICATION.TYPE,
    NOTIFICATION.ACTOR_IDS,
    NOTIFICATION.CREATED_AT,
    NOTIFICATION.CHECKED_AT,
    NOTIFICATION.SEEN_AT,
    NOTIFICATION.COMMUNITY_ID,
)
