package hero.repository.session

import hero.model.Session
import hero.model.SignInProvider
import hero.sql.jooq.Tables.SESSION
import hero.sql.jooq.getSingle
import hero.sql.jooq.tables.records.SessionRecord
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.postgres.extensions.types.Inet
import java.net.InetAddress
import java.util.UUID

class SessionRepository(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    constructor(context: DSLContext) : this(lazy { context })

    fun save(session: Session) {
        val sessionRecord = JooqSessionHelper.mapEntityToRecord(session)

        context.insertInto(SESSION)
            .set(sessionRecord)
            .onDuplicateKeyUpdate()
            .set(sessionRecord)
            .execute()
    }

    fun getById(sessionId: UUID): Session =
        context
            .select(JooqSessionHelper.sessionFields)
            .from(SESSION)
            .where(SESSION.ID.eq(sessionId))
            .getSingle(JooqSessionHelper::mapRecordToEntity)

    fun getById(sessionId: String): Session = getById(UUID.fromString(sessionId))

    fun findById(sessionId: UUID): Session? =
        context
            .select(JooqSessionHelper.sessionFields)
            .from(SESSION)
            .where(SESSION.ID.eq(sessionId))
            .fetchOne()
            ?.let {
                JooqSessionHelper.mapRecordToEntity(it)
            }
}

object JooqSessionHelper {
    fun mapRecordToEntity(record: Record): Session =
        Session(
            id = record[SESSION.ID].toString(),
            userId = record[SESSION.USER_ID],
            userAgent = record[SESSION.USER_AGENT],
            ipAddress = record[SESSION.IP_ADDRESS]?.toString(),
            signInLocation = record[SESSION.SIGN_IN_LOCATION],
            createdAt = record[SESSION.CREATED_AT],
            refreshedAt = record[SESSION.REFRESHED_AT],
            revoked = record[SESSION.REVOKED],
            deviceId = record[SESSION.DEVICE_ID],
            signInProvider = record[SESSION.SIGN_IN_PROVIDER]?.let { SignInProvider.valueOf(it) },
        )

    fun mapEntityToRecord(session: Session): SessionRecord =
        SessionRecord().apply {
            this.id = UUID.fromString(session.id)
            this.userId = session.userId
            this.userAgent = session.userAgent
            this.ipAddress = Inet.inet(InetAddress.getByName(session.ipAddress))
            this.signInLocation = session.signInLocation
            this.createdAt = session.createdAt
            this.refreshedAt = session.refreshedAt
            this.revoked = session.revoked
            this.deviceId = session.deviceId
            this.signInProvider = session.signInProvider?.name
        }

    val sessionFields = listOf(
        SESSION.ID,
        SESSION.USER_ID,
        SESSION.USER_AGENT,
        SESSION.IP_ADDRESS,
        SESSION.SIGN_IN_LOCATION,
        SESSION.CREATED_AT,
        SESSION.REFRESHED_AT,
        SESSION.REVOKED,
        SESSION.DEVICE_ID,
        SESSION.SIGN_IN_PROVIDER,
    )
}
