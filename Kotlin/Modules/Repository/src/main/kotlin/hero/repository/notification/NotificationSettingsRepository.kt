package hero.repository.notification

import hero.model.NotificationsEnabled
import hero.sql.jooq.Tables.NOTIFICATION_SETTINGS
import hero.sql.jooq.tables.records.NotificationSettingsRecord
import org.jooq.DSLContext

class NotificationSettingsRepository(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    constructor(context: <PERSON><PERSON>ontext) : this(lazy { context })

    fun save(
        userId: String,
        settings: NotificationsEnabled,
    ): NotificationsEnabled {
        val notificationSettingsRecord = NotificationSettingsRecord()
            .apply {
                this.userId = userId
                termsChanged = settings.termsChanged
                newsletter = settings.newsletter
                pushNewComment = settings.pushNewComment
                pushNewPost = settings.pushNewPost
                pushNewMessage = settings.pushNewMessage
                emailNewDm = settings.emailNewDm
                emailNewPost = settings.emailNewPost
            }

        context
            .insertInto(NOTIFICATION_SETTINGS)
            .set(notificationSettingsRecord)
            .onDuplicateKeyUpdate()
            .set(notificationSettingsRecord)
            .execute()

        return settings
    }

    fun getByUserId(userId: String): NotificationsEnabled =
        context
            .select(
                NOTIFICATION_SETTINGS.EMAIL_NEW_POST,
                NOTIFICATION_SETTINGS.EMAIL_NEW_DM,
                NOTIFICATION_SETTINGS.NEWSLETTER,
                NOTIFICATION_SETTINGS.TERMS_CHANGED,
                NOTIFICATION_SETTINGS.PUSH_NEW_POST,
                NOTIFICATION_SETTINGS.PUSH_NEW_COMMENT,
                NOTIFICATION_SETTINGS.PUSH_NEW_MESSAGE,
            )
            .from(NOTIFICATION_SETTINGS)
            .where(NOTIFICATION_SETTINGS.USER_ID.eq(userId))
            .fetchSingle()
            .let {
                NotificationsEnabled(
                    termsChanged = it[NOTIFICATION_SETTINGS.TERMS_CHANGED],
                    newsletter = it[NOTIFICATION_SETTINGS.NEWSLETTER],
                    pushNewComment = it[NOTIFICATION_SETTINGS.PUSH_NEW_COMMENT],
                    pushNewPost = it[NOTIFICATION_SETTINGS.PUSH_NEW_POST],
                    pushNewMessage = it[NOTIFICATION_SETTINGS.PUSH_NEW_MESSAGE],
                    emailNewDm = it[NOTIFICATION_SETTINGS.EMAIL_NEW_DM],
                    emailNewPost = it[NOTIFICATION_SETTINGS.EMAIL_NEW_POST],
                )
            }
}
