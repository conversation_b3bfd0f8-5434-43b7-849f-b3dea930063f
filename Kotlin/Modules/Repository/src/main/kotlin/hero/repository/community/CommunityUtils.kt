package hero.repository.community

import hero.baseutils.log
import hero.core.logging.Logger
import hero.model.CommunityMemberStatus
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import hero.sql.jooq.tables.records.CommunityMemberRecord
import org.jooq.DSLContext
import java.time.Instant
import java.util.UUID

fun DSLContext.fetchMemberCommunityIds(userId: String): Set<UUID> =
    this
        .select(COMMUNITY_MEMBER.COMMUNITY_ID)
        .from(COMMUNITY_MEMBER)
        .where(COMMUNITY_MEMBER.USER_ID.eq(userId))
        .and(COMMUNITY_MEMBER.STATE.eq(CommunityMemberStatus.ACTIVE.name))
        .fetch()
        .map { it.value1() }
        .toSet()

fun fetchOwnedCommunityIds(
    context: DSLContext,
    ownerId: String,
): Set<UUID> =
    context
        .select(Tables.COMMUNITY.ID)
        .from(Tables.COMMUNITY)
        .where(Tables.COMMUNITY.OWNER_ID.eq(ownerId))
        .and(Tables.COMMUNITY.DELETED_AT.isNull)
        .fetch()
        .map { it.value1() }
        .toSet()

/**
 * Returns true if user was added to the community, false if he was already a member.
 */
fun addCommunityMember(
    context: DSLContext,
    communityId: UUID,
    userId: String,
    now: Instant = Instant.now(),
    logger: Logger = log,
): Boolean {
    logger.info("Adding $userId to community $communityId")
    val communityMember = context.selectFrom(COMMUNITY_MEMBER)
        .where(COMMUNITY_MEMBER.COMMUNITY_ID.eq(communityId))
        .and(COMMUNITY_MEMBER.USER_ID.eq(userId))
        .fetchOne()

    if (communityMember != null && communityMember.state == CommunityMemberStatus.ACTIVE.name) {
        logger.info("User $userId is already a member of community $communityId, skipping.")
        return false
    }

    if (communityMember == null) {
        val newCommunityMember = CommunityMemberRecord().apply {
            this.communityId = communityId
            this.userId = userId
            this.joinedAt = now
            this.updatedAt = now
            this.createdAt = now
            this.state = CommunityMemberStatus.ACTIVE.name
        }

        context.insertInto(COMMUNITY_MEMBER)
            .set(newCommunityMember)
            .execute()
    } else {
        val updatedCount = context
            .update(COMMUNITY_MEMBER)
            .set(COMMUNITY_MEMBER.STATE, CommunityMemberStatus.ACTIVE.name)
            .setNull(COMMUNITY_MEMBER.LEFT_AT)
            .set(COMMUNITY_MEMBER.JOINED_AT, now)
            .set(COMMUNITY_MEMBER.UPDATED_AT, now)
            .where(COMMUNITY_MEMBER.COMMUNITY_ID.eq(communityId))
            .and(COMMUNITY_MEMBER.USER_ID.eq(userId))
            .execute()

        if (updatedCount != 1) {
            error(
                "Expected to update one row but updated $updatedCount rows for" +
                    " user $userId and community $communityId",
            )
        }
    }

    return true
}
