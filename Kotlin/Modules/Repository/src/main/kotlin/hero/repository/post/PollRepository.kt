package hero.repository.post

import hero.model.Poll
import hero.model.PollOption
import hero.sql.jooq.Tables.POLL
import hero.sql.jooq.Tables.POLL_OPTION
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.impl.DSL
import org.jooq.impl.DSL.multiset

class PollRepository(
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    constructor(context: DSLContext) : this(lazy { context })

    fun findById(pollId: String): Poll? =
        context
            .select(JooqPollHelper.pollFields)
            .from(POLL)
            .where(POLL.ID.eq(pollId))
            .fetchOne()
            ?.map {
                JooqPollHelper.mapRecordToEntity(it)
            }
}

object JooqPollHelper {
    fun mapRecordToEntity(record: Record): Poll =
        Poll(
            id = record[POLL.ID],
            deadline = record[POLL.DEADLINE],
            options = record[pollOptionsField]
                .map { option ->
                    PollOption(
                        id = option[POLL_OPTION.ID],
                        title = option[POLL_OPTION.TITLE],
                        voteCount = option[POLL_OPTION.VOTE_COUNT].toLong(),
                        index = option[POLL_OPTION.INDEX],
                    )
                }
                .associateBy { it.id },
        )

    val pollFields = listOf(
        POLL.ID,
        POLL.DEADLINE,
        POLL.USER_ID,
        pollOptionsField,
    )
}

private val pollOptionsField = multiset(
    DSL
        .select(
            POLL_OPTION.ID,
            POLL_OPTION.TITLE,
            POLL_OPTION.VOTE_COUNT,
            POLL_OPTION.INDEX,
        )
        .from(POLL_OPTION)
        .where(POLL_OPTION.POLL_ID.eq(POLL.ID))
        .orderBy(POLL_OPTION.ID.asc()),
)
