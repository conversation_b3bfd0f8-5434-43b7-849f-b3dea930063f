import nu.studer.gradle.jooq.JooqGenerate
import org.flywaydb.core.Flyway
import org.flywaydb.core.api.configuration.FluentConfiguration
import org.flywaydb.gradle.task.FlywayMigrateTask
import org.jooq.meta.jaxb.Configuration
import org.jooq.meta.jaxb.ForcedType
import org.testcontainers.containers.JdbcDatabaseContainer
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName


plugins {
    id("hero.kotlin-conventions")
    id("org.flywaydb.flyway")
    id("nu.studer.jooq")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Exceptions"))

    runtimeOnly("com.google.cloud.sql:postgres-socket-factory:_")
    implementation("org.postgresql:postgresql:_")
    implementation("com.zaxxer:HikariCP:_")
    implementation("org.flywaydb:flyway-core:_")

    implementation("org.jooq:jooq-postgres-extensions:_")
    api("org.jooq:jooq:_")
    runtimeOnly("org.flywaydb:flyway-database-postgresql:_")

    jooqGenerator("org.postgresql:postgresql:_")
    jooqGenerator("jakarta.xml.bind:jakarta.xml.bind-api:_")
}

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("org.testcontainers:postgresql:_")
        classpath("org.postgresql:postgresql:_")
        classpath("org.flywaydb:flyway-database-postgresql:_")
    }
}

fun startContainer(imageName: String): JdbcDatabaseContainer<*> {
    val container = PostgreSQLContainer<Nothing>(DockerImageName.parse(imageName))
    container.start()
    gradle.buildFinished {
        container.stop()
    }
    return container
}

fun flywayMigrate(
    container: JdbcDatabaseContainer<*>,
    migrationFilesLocation: String,
) {
    val configuration: FluentConfiguration = Flyway.configure()
        .dataSource(container.jdbcUrl, container.username, container.password)
        .locations("filesystem:$migrationFilesLocation")
    val flyway: Flyway = configuration.load()
    flyway.migrate()
}

fun modifyJooqConfiguration(
    jooqGenerate: JooqGenerate,
    container: JdbcDatabaseContainer<*>,
) {
    val jooqConfigurationField = JooqGenerate::class.java.getDeclaredField("jooqConfiguration")
    jooqConfigurationField.isAccessible = true
    val jooqConfiguration = jooqConfigurationField.get(jooqGenerate) as Configuration

    jooqConfiguration.jdbc.apply {
        url = container.jdbcUrl
        user = container.username
        password = container.password
    }
}

// see README.md for running manually the classes generation
jooq {
    configurations {
        create("main") {
            jooqConfiguration.apply {
                logging = org.jooq.meta.jaxb.Logging.WARN
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        inputSchema = "public"
                        forcedTypes = listOf(
                            ForcedType()
                                .withName("INSTANT")
                                .withIncludeTypes("(?i:TIMESTAMP\\ WITH\\ TIME\\ ZONE)"),
                            ForcedType()
                                .withName("INSTANT")
                                .withIncludeTypes("(?i:TIMESTAMP)"),
                            ForcedType()
                                .withUserType("org.jooq.postgres.extensions.types.Inet")
                                .withBinding("org.jooq.postgres.extensions.bindings.InetBinding")
                                .withIncludeTypes("inet"),
                        )
                    }
                    target.apply {
                        packageName = "hero.sql.jooq"
                    }
                }
            }
        }
    }
}

tasks.register("flywayMigrateDevel", FlywayMigrateTask::class) {
    configure("devel")
}

tasks.register("flywayMigrateStaging", FlywayMigrateTask::class) {
    configure("staging")
}

tasks.register("flywayMigrateProd", FlywayMigrateTask::class) {
    configure("prod")
}

tasks.register("flywayMigrateLocal", FlywayMigrateTask::class) {
    configure("local")
}

fun FlywayMigrateTask.configure(env: String) {
    description = "Migrate the $env database"
    user = "postgres"
    password = System.getenv("POSTGRES_USER_PWD") ?: "postgres"
    url = "*************************************************"
}

tasks.named<JooqGenerate>("generateJooq") {
    val migrationFilesLocation = "$projectDir/src/main/resources/db/migration"
    inputs.files(fileTree(migrationFilesLocation))
        .withPropertyName("migrations")
        .withPathSensitivity(PathSensitivity.RELATIVE)

    allInputsDeclared.set(true)
    outputs.cacheIf {
        true
    }

    doFirst {
        val container = startContainer("postgres:14-alpine:_")
        flywayMigrate(container, migrationFilesLocation)
        modifyJooqConfiguration(this as JooqGenerate, container)
    }
}
