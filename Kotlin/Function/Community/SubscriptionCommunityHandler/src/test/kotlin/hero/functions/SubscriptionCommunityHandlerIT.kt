package hero.functions

import hero.baseutils.minusDays
import hero.model.CommunityMemberStatus
import hero.model.SupportCounts
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class SubscriptionCommunityHandlerIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(1755450161)

    @Test
    fun `should remove user from communities when unsubscribed`() {
        val underTest = prepareFunction()

        testHelper.createUser("filip")
        testHelper.createUser("cestmir")
        testHelper.createUser("other-creator")

        val community1 = testHelper.createCommunity("cestmir", name = "Community 1")
        val community2 = testHelper.createCommunity("cestmir", name = "Community 2")
        val community3 = testHelper.createCommunity("other-creator", name = "Other Community")

        // Add filip as active member to all comunities
        testHelper.createCommunityMember(community1.id, "filip", CommunityMemberStatus.ACTIVE)
        testHelper.createCommunityMember(community2.id, "filip", CommunityMemberStatus.ACTIVE)
        testHelper.createCommunityMember(community3.id, "filip", CommunityMemberStatus.ACTIVE)

        underTest.consume(subscribeEvent("filip", "cestmir", SubscriberStatusChange.UNSUBSCRIBED))

        // Verify filip was removed from cestmir's communities but not from other creator's community
        val communityMembers = testContext.selectFrom(COMMUNITY_MEMBER).fetch()
        with(communityMembers.first { it.communityId == community1.id && it.userId == "filip" }) {
            assertThat(state).isEqualTo(CommunityMemberStatus.SUBSCRIPTION_EXPIRED.name)
            assertThat(leftAt).isNotNull()
        }

        with(communityMembers.first { it.communityId == community2.id && it.userId == "filip" }) {
            assertThat(state).isEqualTo(CommunityMemberStatus.SUBSCRIPTION_EXPIRED.name)
            assertThat(leftAt).isNotNull()
        }

        with(communityMembers.first { it.communityId == community3.id && it.userId == "filip" }) {
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(leftAt).isNull()
        }

        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("filip")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(1)
        }
        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("cestmir")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(0)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community1.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(0)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community2.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(0)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community3.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(1)
        }
    }

    @Test
    fun `should add user to communities when subscribed`() {
        val underTest = prepareFunction()

        testHelper.createUser("pavel")
        testHelper.createUser("cestmir")

        val community1 = testHelper.createCommunity("cestmir", name = "Community 1")
        val community2 = testHelper.createCommunity("cestmir", name = "Community 2")
        val deletedCommunity = testHelper.createCommunity(
            "cestmir",
            name = "Deleted Community",
            deletedAt = Instant.now(),
        )

        underTest.consume(subscribeEvent("pavel", "cestmir", SubscriberStatusChange.SUBSCRIBED))

        // Verify pavel was added to active communities but not deleted ones
        val communityMembers = testContext.selectFrom(COMMUNITY_MEMBER).fetch()
        assertThat(communityMembers).hasSize(2)

        with(communityMembers.first { it.communityId == community1.id }) {
            assertThat(userId).isEqualTo("pavel")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(joinedAt).isNotNull()
        }

        with(communityMembers.first { it.communityId == community2.id }) {
            assertThat(userId).isEqualTo("pavel")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(joinedAt).isNotNull()
        }

        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("pavel")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(2)
        }
        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("cestmir")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(0)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community1.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(1)
        }

        // Verify no membership was created for deleted community
        val deletedCommunityMembers = communityMembers.filter { it.communityId == deletedCommunity.id }
        assertThat(deletedCommunityMembers).isEmpty()
    }

    @Test
    fun `should add user to the community when he was a member before`() {
        val underTest = prepareFunction()

        testHelper.createUser("filip")
        testHelper.createUser("cestmir")

        val community = testHelper.createCommunity("cestmir", name = "Community 1")
        val joinedAt = expectedTimestamp.minusDays(2)
        testHelper.createCommunityMember(
            community.id,
            "filip",
            CommunityMemberStatus.SUBSCRIPTION_EXPIRED,
            joinedAt = joinedAt,
            leftAt = expectedTimestamp.minusDays(1),
        )

        underTest.consume(subscribeEvent("filip", "cestmir", SubscriberStatusChange.SUBSCRIBED))

        with(testContext.selectFrom(COMMUNITY_MEMBER).where(COMMUNITY_MEMBER.USER_ID.eq("filip")).fetchSingle()) {
            assertThat(createdAt).isEqualTo(joinedAt)
            assertThat(updatedAt).isEqualTo(expectedTimestamp)
            assertThat(this.joinedAt).isEqualTo(expectedTimestamp)
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(leftAt).isNull()
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(1)
        }
        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("filip")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(1)
        }
    }

    @Test
    fun `shouldn't do anything if user is already a member of the community`() {
        val underTest = prepareFunction()

        testHelper.createUser("filip", counts = SupportCounts(joinedCommunities = 10))
        testHelper.createUser("cestmir")

        val community = testHelper.createCommunity("cestmir", name = "Community 1", membersCount = 10)
        val now = Instant.ofEpochSecond(100)
        testHelper.createCommunityMember(community.id, "filip", state = CommunityMemberStatus.ACTIVE, joinedAt = now)

        underTest.consume(subscribeEvent("filip", "cestmir", SubscriberStatusChange.SUBSCRIBED))

        with(testContext.selectFrom(COMMUNITY_MEMBER).where(COMMUNITY_MEMBER.USER_ID.eq("filip")).fetchSingle()) {
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(leftAt).isNull()
            assertThat(joinedAt).isEqualTo(now)
            assertThat(createdAt).isEqualTo(now)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(11)
        }
        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("filip")).fetchSingle()) {
            // this must be 10, since we haven't updated the counts to really reflect the count
            assertThat(joinedCommunitiesCount).isEqualTo(10)
        }
    }

    private fun prepareFunction(): SubscriptionCommunityHandler =
        SubscriptionCommunityHandler(
            lazyTestContext,
            TestEnvironmentVariables,
            TestLogger,
            TestClock(expectedTimestamp),
        )

    private fun subscribeEvent(
        userId: String,
        creatorId: String,
        statusChange: SubscriberStatusChange,
    ): SubscriberStatusChanged =
        SubscriberStatusChanged(
            userId,
            creatorId,
            statusChange,
            false,
            "",
            false,
            false,
            false,
        )
}
