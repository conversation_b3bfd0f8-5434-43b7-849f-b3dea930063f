package hero.functions

import hero.model.NotificationType
import hero.model.StorageEntityType
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ThreadStateChangedHandlerIT : IntegrationTest() {
    @Test
    fun `should handle thread state change - update threads count`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        testHelper.createUser("pablo")

        val community = testHelper.createCommunity("cestmir")
        testHelper.createCommunityMember(community.id, "cestmir")
        testHelper.createCommunityMember(community.id, "pablo")

        // post from different user in community
        testHelper.createPost("pablo", communityId = community.id)
        testHelper.createPost("cestmir", communityId = community.id)
        // one deleted post, shouldn't be counted
        testHelper.createPost("cestmir", communityId = community.id, state = PostState.DELETED)
        val thread = testHelper.createPost("cestmir", communityId = community.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, thread))

        with(testContext.selectFrom(Tables.COMMUNITY).fetchSingle()) {
            assertThat(threadsCount).isEqualTo(3)
        }

        with(testContext.selectFrom(COMMUNITY_MEMBER).where(COMMUNITY_MEMBER.USER_ID.eq("cestmir")).fetchSingle()) {
            assertThat(threadsCount).isEqualTo(2)
        }
    }

    @Test
    fun `should create new notification when a thread is published`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")
        val thread = testHelper.createPost("pablo", communityId = community.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, thread))

        with(TestRepositories.notificationRepository.find { this }.first()) {
            assertThat(type).isEqualTo(NotificationType.NEW_THREAD)
            assertThat(objectId).isEqualTo(thread.id)
            assertThat(created).isEqualTo(thread.published)
            assertThat(actorIds).containsExactly(thread.userId)
            assertThat(objectType).isEqualTo(StorageEntityType.POST)
            assertThat(communityId).isEqualTo(community.id.toString())
        }
    }

    @Test
    fun `should not create notification if community owner posts a thread`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")
        val thread = testHelper.createPost("cestmir", communityId = community.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, thread))

        assertThat(TestRepositories.notificationRepository.find { this }).isEmpty()
    }

    @Test
    fun `should delete notification when a thread is deleted`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")
        val thread = testHelper.createPost("pablo", communityId = community.id, state = PostState.DELETED)
        val notification = testHelper.createNotification(
            "cestmir",
            objectId = thread.id,
            type = NotificationType.NEW_THREAD,
            actorIds = listOf("pablo"),
            objectType = StorageEntityType.POST,
        )

        underTest.consume(PostStateChanged(PostStateChange.DELETED, thread))

        with(testContext.selectFrom(Tables.NOTIFICATION).fetch().first()) {
            assertThat(deletedAt).isNotNull
            assertThat(id).isEqualTo(notification.id)
        }
    }

    private fun prepareFunction(): ThreadStateChangedHandler =
        ThreadStateChangedHandler(
            lazyTestContext,
            TestEnvironmentVariables,
            TestRepositories.notificationRepository,
        )
}
