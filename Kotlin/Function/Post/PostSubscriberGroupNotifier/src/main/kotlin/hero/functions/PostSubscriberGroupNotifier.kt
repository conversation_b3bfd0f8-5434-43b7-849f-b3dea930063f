package hero.functions

import com.google.firebase.ErrorCode
import com.google.firebase.messaging.AndroidConfig
import com.google.firebase.messaging.AndroidNotification
import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.ApsAlert
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.baseutils.preventGmailLinks
import hero.baseutils.publicFunctionCall
import hero.baseutils.systemEnvRelaxed
import hero.baseutils.truncate
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.firestore
import hero.jwt.toJwt
import hero.messaging.initFirebaseMessaging
import hero.messaging.sendMulticastMessage
import hero.messaging.setImageScaled
import hero.model.Notification
import hero.model.NotificationType.NEW_LIVESTREAM
import hero.model.NotificationType.NEW_POST
import hero.model.NotificationType.NEW_THREAD
import hero.model.Post
import hero.model.StorageEntityType
import hero.model.User
import hero.model.UserStatus
import hero.model.topics.EmailPublished
import hero.model.topics.PostNotificationGroupPrepared
import hero.repository.device.fetchFirebaseRegistrationTokens
import hero.repository.notification.NotificationRepository
import hero.repository.post.PostRepository
import hero.repository.user.UserRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.COMMUNITY
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import java.util.UUID
import com.google.firebase.messaging.Notification as FirebaseNotification

@Suppress("unused")
class PostSubscriberGroupNotifier(
    private val hostname: String = SystemEnv.hostname,
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, SystemEnv.isProduction),
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
    private val firebaseMessaging: FirebaseMessaging = initFirebaseMessaging(
        SystemEnv.cloudProject,
        production.envPrefix,
    ),
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val postRepository: PostRepository = PostRepository(lazyContext),
    private val userRepository: UserRepository = UserRepository(lazyContext),
    private val notificationRepository: NotificationRepository = NotificationRepository(lazyContext),
) : PubSubSubscriber<PostNotificationGroupPrepared>() {
    private val context: DSLContext by lazyContext

    override fun consume(payload: PostNotificationGroupPrepared) {
        val creator = userRepository.findById(payload.creatorId)
        if (creator == null || creator.status != UserStatus.ACTIVE) {
            return
        }

        log.info(
            "Notifying group of ${payload.subscriberIds.size} subscribers for a new posts ${payload.postIds}.",
            mapOf("creatorId" to creator.id, "postIds" to payload.postIds),
        )
        val posts = postRepository.find {
            this
                .where(Tables.POST.ID.`in`(payload.postIds))
                .orderBy(Tables.POST.PUBLISHED_AT.asc())
        }

        val subscribers = userRepository.find { where(Tables.USER.ID.`in`(payload.subscriberIds)) }

        runBlocking {
            val notifications = subscribers
                .filter { subscriber -> subscriber.status == UserStatus.ACTIVE }
                .map { subscriber -> async { notify(creator, subscriber, posts) } }
                .awaitAll()
                .flatten()

            notificationRepository.saveAll(notifications)
        }
    }

    private fun notify(
        creator: User,
        subscriber: User,
        posts: List<Post>,
    ): List<Notification> {
        val postIds = posts.map { it.id }
        try {
            val postWithNotifications: List<Pair<Post, Notification>> = posts.mapNotNull {
                val postAuthor = userRepository.getById(it.userId)
                if (postAuthor == subscriber) {
                    // no need to notify the author
                    return@mapNotNull null
                }
                val notification = notifyInUi(creator, postAuthor, subscriber, it)
                notifyByEmail(creator, postAuthor, subscriber, it)
                it to notification
            }

            val (lastPost, lastNotification) = postWithNotifications.lastOrNull() ?: return emptyList()
            notifyByPush(creator, subscriber, lastPost, lastNotification)

            return postWithNotifications.map { it.second }
        } catch (e: Throwable) {
            log.fatal(
                "Cannot notify User ${subscriber.id} for Creator ${creator.id} and posts $postIds.",
                mapOf("creatorId" to creator.id, "userId" to subscriber.id, "postIds" to postIds),
                e,
            )

            return emptyList()
        }
    }

    private fun notifyByPush(
        creator: User,
        subscriber: User,
        post: Post,
        notification: Notification,
    ) {
        if (systemEnvRelaxed("FF_PUSH_NOTIFICATION") != "enabled") {
            log.info("Push notifications are disabled in ${SystemEnv.environment}")
            return
        }

        if (post.communityId == null && !subscriber.notificationsEnabled.pushNewPost) {
            return
        }

        if (post.communityId != null && !subscriber.notificationsEnabled.pushNewThread) {
            return
        }

        if (post.communityId != null && SystemEnv.environment == "prod") {
            // TODO enable production
            return
        }

        val tokens = fetchFirebaseRegistrationTokens(context, subscriber.id)
        if (tokens.isEmpty()) {
            return
        }

        val postAuthor = userRepository.getById(post.userId)
        val message = buildMessage(creator, postAuthor, post, notification)
        sendMulticastMessage(firebaseMessaging, message, tokens, subscriber.id, log)
    }

    private fun buildMessage(
        creator: User,
        postAuthor: User,
        post: Post,
        notification: Notification,
    ): MulticastMessage.Builder =
        MulticastMessage.builder()
            .setNotification(
                FirebaseNotification.builder()
                    // TODO not sure if we want to display postAuthor in community of creator
                    .setTitle(postAuthor.name)
                    .setImageScaled(
                        post.assets.firstNotNullOfOrNull { it.thumbnailImage?.id }
                            ?: post.assets.firstNotNullOfOrNull { it.image?.id }
                            ?: postAuthor.image?.id,
                    )
                    // We use FirebaseNotification text body only when the `post.text` is filled in.
                    // This does not allow the text to be localized, so for generic texts like
                    // "There is a new post", we use localized variant specific for Apple, see below.
                    .apply { if (post.text.isNotEmpty()) setBody(post.text.truncate(80)) }
                    .build(),
            )
            .putData(
                "notification_type",
                when {
                    post.communityId != null -> NEW_THREAD.name
                    post.isLivestream() -> NEW_LIVESTREAM.name
                    else -> NEW_POST.name
                },
            )
            .putData("post_id", post.id)
            .putData("notification_id", notification.id)
            .apply {
                if (post.text.isEmpty()) {
                    val bodyLocalizationKey = when {
                        post.communityId != null -> "push_notification_new_thread_body"
                        post.isLivestream() -> "push_notification_new_livestream_body"
                        else -> "push_notification_new_post_body"
                    }
                    val apsAlert = ApsAlert.builder().setLocalizationKey(bodyLocalizationKey).build()
                    // localised notification body specific to Apple, see above the FirebaseNotification title
                    val aps = Aps.builder().setAlert(apsAlert).build()
                    val apns = ApnsConfig.builder().setAps(aps).build()
                    setApnsConfig(apns)

                    // localised notification body specific to android, see above the FirebaseNotification title
                    val androidAlert = AndroidConfig.builder()
                        .setNotification(
                            AndroidNotification.builder()
                                .setBodyLocalizationKey(bodyLocalizationKey)
                                .build(),
                        )
                        .build()

                    setAndroidConfig(androidAlert)
                }
            }

    private val ignoreCodes = setOf(ErrorCode.NOT_FOUND)
    private val retryCodes = setOf(ErrorCode.UNKNOWN, ErrorCode.INTERNAL, ErrorCode.UNAVAILABLE)

    private fun notifyByEmail(
        creator: User,
        postAuthor: User,
        subscriber: User,
        post: Post,
    ) {
        if (subscriber.email.isNullOrBlank()) {
            return
        }
        if (post.communityId == null && !subscriber.notificationsEnabled.emailNewPost) {
            return
        }
        if (post.communityId != null) {
            // TODO skipping all community posts for now
            return
        }

        val community = post.communityId?.let {
            context
                .selectFrom(COMMUNITY)
                .where(COMMUNITY.ID.eq(UUID.fromString(it)))
                .fetchSingle()
        }

        val jwt = mapOf(
            "userId" to subscriber.id,
            "notificationType" to if (post.communityId != null) NEW_THREAD else NEW_POST,
        ).toJwt()

        val thumbnail = post.assets.firstNotNullOfOrNull { it.thumbnail ?: it.image?.id }
        val postLink = when {
            community != null -> "$hostname/community/${community.slug}/thread/${post.id}"
            else -> "$hostname/${creator.path}/post/${post.id}"
        }

        pubSub.publish(
            EmailPublished(
                from = creator.name,
                to = subscriber.email!!,
                // https://gitlab.com/-/ide/project/heroheroco/mailgun-templates/tree/main/-/templates/
                template = when {
                    post.communityId != null -> "new-thread"
                    else -> "new-posts"
                },
                variables = listOf(
                    "post-link" to postLink,
                    "user-name" to subscriber.name,
                    "creator-name" to creator.name,
                    "post-author-name" to postAuthor.name,
                    "community-name" to community?.name,
                    "preview" to post.text.truncate(300).preventGmailLinks(),
                    "thumbnail" to thumbnail,
                    "is-livestream" to post.isLivestream(),
                    "is-livestream-live" to post.isLivestreamLive(),
                    "unsubscribe-link" to publicFunctionCall("notification-disabler") + "?request=$jwt",
                ),
                language = subscriber.language,
            ),
        )
    }

    private fun notifyInUi(
        creator: User,
        postAuthor: User,
        subscriber: User,
        post: Post,
    ): Notification =
        Notification(
            userId = subscriber.id,
            type = when {
                post.communityId != null -> NEW_THREAD
                post.isLivestream() -> NEW_LIVESTREAM
                else -> NEW_POST
            },
            actorIds = mutableListOf(postAuthor.id),
            objectType = StorageEntityType.POST,
            objectId = post.id,
            created = post.published,
            timestamp = post.published,
            communityId = post.communityId,
        )
}
